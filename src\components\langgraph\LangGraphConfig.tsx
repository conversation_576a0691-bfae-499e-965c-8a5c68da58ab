'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  CogIcon, 
  PlayIcon, 
  PauseIcon,
  ArrowPathIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

interface LangGraphConfigProps {
  onConfigChange?: (config: any) => void;
  onStart?: () => void;
  onStop?: () => void;
  isRunning?: boolean;
  className?: string;
}

export function LangGraphConfig({
  onConfigChange,
  onStart,
  onStop,
  isRunning = false,
  className
}: LangGraphConfigProps) {
  const [config, setConfig] = useState({
    // LLM配置
    llm: {
      provider: 'openai',
      model: 'gpt-4o-mini',
      temperature: 0.1,
      maxTokens: 4000,
    },

    // 分析配置
    analysis: {
      type: 'comprehensive' as 'basic' | 'comprehensive' | 'quick',
      includeRisk: true,
      includeSentiment: true,
      includeNews: true,
      timeframe: '1m' as '1d' | '1w' | '1m' | '3m' | '1y',
    },

    // 工作流配置
    workflow: {
      enableParallelExecution: true,
      maxRetries: 3,
      timeout: 300000, // 5分钟
      checkpointInterval: 10000, // 10秒
      enableStreaming: true,
    },

    // 工具配置
    tools: {
      stockAnalysis: true,
      marketData: true,
      newsAnalysis: true,
      riskAssessment: true,
      customTools: [],
    },

    // 内存配置
    memory: {
      enabled: true,
      maxMessages: 100,
      persistToStorage: true,
    },

    // 实时配置
    realtime: {
      enableWebSocket: true,
      autoReconnect: true,
      heartbeatInterval: 30000, // 30秒
    },

    // 调试配置
    debug: {
      enabled: false,
      logLevel: 'info' as 'debug' | 'info' | 'warn' | 'error',
      traceExecution: false,
      showMetadata: false,
    },
  });

  const handleConfigUpdate = (section: string, key: string, value: any) => {
    const newConfig = {
      ...config,
      [section]: {
        ...config[section as keyof typeof config],
        [key]: value,
      },
    };
    
    setConfig(newConfig);
    onConfigChange?.(newConfig);
  };

  const handleStart = () => {
    onStart?.();
  };

  const handleStop = () => {
    onStop?.();
  };

  const resetConfig = () => {
    setConfig({
      llm: {
        provider: 'openai',
        model: 'gpt-4o-mini',
        temperature: 0.1,
        maxTokens: 4000,
      },
      workflow: {
        enableParallelExecution: true,
        maxRetries: 3,
        timeout: 300000,
        checkpointInterval: 10000,
      },
      tools: {
        stockAnalysis: true,
        marketData: true,
        newsAnalysis: true,
        riskAssessment: true,
        customTools: [],
      },
      memory: {
        enabled: true,
        maxMessages: 100,
        persistToStorage: true,
      },
      debug: {
        enabled: false,
        logLevel: 'info',
        traceExecution: false,
      },
    });
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <CogIcon className="h-5 w-5 text-blue-600" />
            <span>LangGraph 配置</span>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            <Badge variant={isRunning ? 'success' : 'default'}>
              {isRunning ? '运行中' : '已停止'}
            </Badge>
            
            <Button
              size="sm"
              onClick={isRunning ? handleStop : handleStart}
              variant={isRunning ? 'danger' : 'primary'}
            >
              {isRunning ? (
                <>
                  <PauseIcon className="h-4 w-4 mr-1" />
                  停止
                </>
              ) : (
                <>
                  <PlayIcon className="h-4 w-4 mr-1" />
                  启动
                </>
              )}
            </Button>
            
            <Button
              size="sm"
              variant="ghost"
              onClick={resetConfig}
              disabled={isRunning}
            >
              <ArrowPathIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* LLM配置 */}
        <div>
          <h4 className="font-medium text-slate-900 dark:text-white mb-3">
            大语言模型配置
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                模型提供商
              </label>
              <select
                value={config.llm.provider}
                onChange={(e) => handleConfigUpdate('llm', 'provider', e.target.value)}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                disabled={isRunning}
              >
                <option value="openai">OpenAI</option>
                <option value="anthropic">Anthropic</option>
                <option value="local">本地模型</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                模型名称
              </label>
              <select
                value={config.llm.model}
                onChange={(e) => handleConfigUpdate('llm', 'model', e.target.value)}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                disabled={isRunning}
              >
                <option value="gpt-4o-mini">GPT-4O Mini</option>
                <option value="o3-mini-high">O3 Mini High</option>
                <option value="o4-mini">O4 Mini</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                温度 ({config.llm.temperature})
              </label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={config.llm.temperature}
                onChange={(e) => handleConfigUpdate('llm', 'temperature', parseFloat(e.target.value))}
                className="w-full"
                disabled={isRunning}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                最大令牌数
              </label>
              <input
                type="number"
                value={config.llm.maxTokens}
                onChange={(e) => handleConfigUpdate('llm', 'maxTokens', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                disabled={isRunning}
              />
            </div>
          </div>
        </div>

        {/* 分析配置 */}
        <div>
          <h4 className="font-medium text-slate-900 dark:text-white mb-3">
            分析配置
          </h4>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                分析类型
              </label>
              <select
                value={config.analysis.type}
                onChange={(e) => handleConfigUpdate('analysis', 'type', e.target.value)}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                disabled={isRunning}
              >
                <option value="basic">基础分析</option>
                <option value="comprehensive">综合分析</option>
                <option value="quick">快速分析</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                时间范围
              </label>
              <select
                value={config.analysis.timeframe}
                onChange={(e) => handleConfigUpdate('analysis', 'timeframe', e.target.value)}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                disabled={isRunning}
              >
                <option value="1d">1天</option>
                <option value="1w">1周</option>
                <option value="1m">1个月</option>
                <option value="3m">3个月</option>
                <option value="1y">1年</option>
              </select>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="includeRisk"
                  checked={config.analysis.includeRisk}
                  onChange={(e) => handleConfigUpdate('analysis', 'includeRisk', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                  disabled={isRunning}
                />
                <label htmlFor="includeRisk" className="text-sm text-slate-700 dark:text-slate-300">
                  包含风险评估
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="includeSentiment"
                  checked={config.analysis.includeSentiment}
                  onChange={(e) => handleConfigUpdate('analysis', 'includeSentiment', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                  disabled={isRunning}
                />
                <label htmlFor="includeSentiment" className="text-sm text-slate-700 dark:text-slate-300">
                  包含情绪分析
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="includeNews"
                  checked={config.analysis.includeNews}
                  onChange={(e) => handleConfigUpdate('analysis', 'includeNews', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                  disabled={isRunning}
                />
                <label htmlFor="includeNews" className="text-sm text-slate-700 dark:text-slate-300">
                  包含新闻分析
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* 工作流配置 */}
        <div>
          <h4 className="font-medium text-slate-900 dark:text-white mb-3">
            工作流配置
          </h4>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="parallelExecution"
                checked={config.workflow.enableParallelExecution}
                onChange={(e) => handleConfigUpdate('workflow', 'enableParallelExecution', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                disabled={isRunning}
              />
              <label htmlFor="parallelExecution" className="text-sm text-slate-700 dark:text-slate-300">
                启用并行执行
              </label>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                  最大重试次数
                </label>
                <input
                  type="number"
                  value={config.workflow.maxRetries}
                  onChange={(e) => handleConfigUpdate('workflow', 'maxRetries', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                  disabled={isRunning}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                  超时时间 (秒)
                </label>
                <input
                  type="number"
                  value={config.workflow.timeout / 1000}
                  onChange={(e) => handleConfigUpdate('workflow', 'timeout', parseInt(e.target.value) * 1000)}
                  className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                  disabled={isRunning}
                />
              </div>
            </div>
          </div>
        </div>
        
        {/* 工具配置 */}
        <div>
          <h4 className="font-medium text-slate-900 dark:text-white mb-3">
            工具配置
          </h4>
          <div className="grid grid-cols-2 gap-3">
            {Object.entries(config.tools).map(([key, value]) => {
              if (key === 'customTools') return null;
              
              return (
                <div key={key} className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id={key}
                    checked={value as boolean}
                    onChange={(e) => handleConfigUpdate('tools', key, e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                    disabled={isRunning}
                  />
                  <label htmlFor={key} className="text-sm text-slate-700 dark:text-slate-300">
                    {key === 'stockAnalysis' ? '股票分析' :
                     key === 'marketData' ? '市场数据' :
                     key === 'newsAnalysis' ? '新闻分析' :
                     key === 'riskAssessment' ? '风险评估' : key}
                  </label>
                </div>
              );
            })}
          </div>
        </div>
        
        {/* 内存配置 */}
        <div>
          <h4 className="font-medium text-slate-900 dark:text-white mb-3">
            内存配置
          </h4>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="memoryEnabled"
                checked={config.memory.enabled}
                onChange={(e) => handleConfigUpdate('memory', 'enabled', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                disabled={isRunning}
              />
              <label htmlFor="memoryEnabled" className="text-sm text-slate-700 dark:text-slate-300">
                启用内存功能
              </label>
            </div>
            
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="persistToStorage"
                checked={config.memory.persistToStorage}
                onChange={(e) => handleConfigUpdate('memory', 'persistToStorage', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                disabled={isRunning || !config.memory.enabled}
              />
              <label htmlFor="persistToStorage" className="text-sm text-slate-700 dark:text-slate-300">
                持久化到存储
              </label>
            </div>
          </div>
        </div>
        
        {/* 调试配置 */}
        <div>
          <h4 className="font-medium text-slate-900 dark:text-white mb-3">
            调试配置
          </h4>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="debugEnabled"
                checked={config.debug.enabled}
                onChange={(e) => handleConfigUpdate('debug', 'enabled', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                disabled={isRunning}
              />
              <label htmlFor="debugEnabled" className="text-sm text-slate-700 dark:text-slate-300">
                启用调试模式
              </label>
            </div>
            
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="traceExecution"
                checked={config.debug.traceExecution}
                onChange={(e) => handleConfigUpdate('debug', 'traceExecution', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                disabled={isRunning || !config.debug.enabled}
              />
              <label htmlFor="traceExecution" className="text-sm text-slate-700 dark:text-slate-300">
                跟踪执行过程
              </label>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
