'use client';

import React, { useState } from 'react';
import { LangGraphChat } from '@/components/langgraph/LangGraphChat';
import { LangGraphConfig } from '@/components/langgraph/LangGraphConfig';
import { LangGraphStatusMonitor } from '@/components/langgraph/LangGraphStatusMonitor';
import { LangGraphExample } from '@/components/examples/LangGraphExample';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export default function LangGraphDemoPage() {
  const [activeTab, setActiveTab] = useState<'chat' | 'config' | 'monitor' | 'example'>('chat');
  const [ticker, setTicker] = useState('AAPL');
  const [isRunning, setIsRunning] = useState(false);
  const [config, setConfig] = useState<any>(null);

  const handleConfigChange = (newConfig: any) => {
    setConfig(newConfig);
    console.log('配置更新:', newConfig);
  };

  const handleStart = () => {
    setIsRunning(true);
    console.log('启动 LangGraph 服务');
  };

  const handleStop = () => {
    setIsRunning(false);
    console.log('停止 LangGraph 服务');
  };

  const handleAnalysisComplete = (result: any) => {
    console.log('分析完成:', result);
  };

  const tabs = [
    { id: 'chat', label: '智能对话', icon: '💬' },
    { id: 'config', label: '配置管理', icon: '⚙️' },
    { id: 'monitor', label: '状态监控', icon: '📊' },
    { id: 'example', label: '完整示例', icon: '🚀' },
  ];

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white mb-2">
            LangGraph 智能交易分析平台
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            基于 LangGraph 的新一代智能交易分析系统 - 前后端分离架构演示
          </p>
          
          {/* 架构特性 */}
          <div className="flex flex-wrap gap-2 mt-4">
            <Badge variant="success">✅ 后端 LangGraph 服务</Badge>
            <Badge variant="info">🔄 实时状态同步</Badge>
            <Badge variant="warning">⚡ 流式响应</Badge>
            <Badge variant="secondary">🔒 安全架构</Badge>
            <Badge variant="primary">📈 可扩展设计</Badge>
          </div>
        </div>

        {/* 股票选择器 */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>股票选择</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <label className="block text-sm font-medium mb-1">
                  股票代码
                </label>
                <input
                  type="text"
                  value={ticker}
                  onChange={(e) => setTicker(e.target.value.toUpperCase())}
                  className="w-full px-3 py-2 border rounded-lg"
                  placeholder="输入股票代码，如 AAPL, TSLA, NVDA"
                />
              </div>
              <div className="flex items-center gap-2">
                <Button
                  onClick={() => setTicker('AAPL')}
                  variant={ticker === 'AAPL' ? 'primary' : 'ghost'}
                  size="sm"
                >
                  AAPL
                </Button>
                <Button
                  onClick={() => setTicker('TSLA')}
                  variant={ticker === 'TSLA' ? 'primary' : 'ghost'}
                  size="sm"
                >
                  TSLA
                </Button>
                <Button
                  onClick={() => setTicker('NVDA')}
                  variant={ticker === 'NVDA' ? 'primary' : 'ghost'}
                  size="sm"
                >
                  NVDA
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 标签页导航 */}
        <div className="mb-6">
          <div className="border-b border-slate-200 dark:border-slate-700">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* 标签页内容 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 主要内容区域 */}
          <div className="lg:col-span-2">
            {activeTab === 'chat' && (
              <LangGraphChat
                ticker={ticker}
                onAnalysisComplete={handleAnalysisComplete}
                className="h-[600px]"
              />
            )}

            {activeTab === 'config' && (
              <LangGraphConfig
                onConfigChange={handleConfigChange}
                onStart={handleStart}
                onStop={handleStop}
                isRunning={isRunning}
                className="h-[600px] overflow-y-auto"
              />
            )}

            {activeTab === 'monitor' && (
              <LangGraphStatusMonitor className="h-[600px] overflow-y-auto" />
            )}

            {activeTab === 'example' && (
              <div className="h-[600px] overflow-y-auto">
                <LangGraphExample />
              </div>
            )}
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 快速状态 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">快速状态</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">服务状态</span>
                  <Badge variant={isRunning ? 'success' : 'secondary'}>
                    {isRunning ? '运行中' : '已停止'}
                  </Badge>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm">当前股票</span>
                  <code className="text-sm bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded">
                    {ticker}
                  </code>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm">活跃标签</span>
                  <span className="text-sm text-slate-600 dark:text-slate-400">
                    {tabs.find(t => t.id === activeTab)?.label}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* 架构说明 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">架构优势</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="flex items-start gap-2">
                  <span className="text-green-500">✓</span>
                  <span>LangGraph 逻辑完全在后端运行</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-green-500">✓</span>
                  <span>前端专注于状态管理和 UI</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-green-500">✓</span>
                  <span>支持实时状态更新</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-green-500">✓</span>
                  <span>流式响应提升用户体验</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-green-500">✓</span>
                  <span>API 密钥安全保护</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-green-500">✓</span>
                  <span>可独立扩展和部署</span>
                </div>
              </CardContent>
            </Card>

            {/* 快速操作 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">快速操作</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button
                  onClick={() => setActiveTab('chat')}
                  variant={activeTab === 'chat' ? 'primary' : 'ghost'}
                  className="w-full justify-start"
                >
                  💬 开始对话
                </Button>
                <Button
                  onClick={() => setActiveTab('config')}
                  variant={activeTab === 'config' ? 'primary' : 'ghost'}
                  className="w-full justify-start"
                >
                  ⚙️ 配置系统
                </Button>
                <Button
                  onClick={() => setActiveTab('monitor')}
                  variant={activeTab === 'monitor' ? 'primary' : 'ghost'}
                  className="w-full justify-start"
                >
                  📊 监控状态
                </Button>
                <Button
                  onClick={() => setActiveTab('example')}
                  variant={activeTab === 'example' ? 'primary' : 'ghost'}
                  className="w-full justify-start"
                >
                  🚀 查看示例
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
