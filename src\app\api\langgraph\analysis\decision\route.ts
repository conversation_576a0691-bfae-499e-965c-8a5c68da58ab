import { NextRequest, NextResponse } from 'next/server';
import { analysisStore } from '../start/route';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const analysisId = searchParams.get('analysisId');

    if (!analysisId) {
      return NextResponse.json(
        { error: '分析ID不能为空' },
        { status: 400 }
      );
    }

    const analysis = analysisStore.get(analysisId);
    
    if (!analysis) {
      return NextResponse.json(
        { error: '分析不存在' },
        { status: 404 }
      );
    }

    // 返回交易决策
    return NextResponse.json({
      analysisId: analysis.analysisId,
      tradingDecision: analysis.tradingDecision,
      isComplete: analysis.isComplete,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('获取交易决策失败:', error);
    return NextResponse.json(
      { error: '获取交易决策失败' },
      { status: 500 }
    );
  }
}
