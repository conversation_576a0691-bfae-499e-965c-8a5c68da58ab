'use client';

import React, { useState } from 'react';
import { useTradingAnalysis } from '@/hooks/useTradingAnalysis';
import { AnalysisConfig } from '@/lib/api';

export default function TestAnalysisPage() {
  const [config, setConfig] = useState<AnalysisConfig>({
    ticker: 'AAPL',
    analysisDate: new Date().toISOString().split('T')[0],
    analysisType: 'comprehensive',
    includeRisk: true,
    includeSentiment: true,
    includeNews: true,
  });

  const {
    analysisId,
    isAnalyzing,
    analysisState,
    agentStatuses,
    reports,
    finalDecision,
    startAnalysis,
    stopAnalysis,
    restartAnalysis,
  } = useTradingAnalysis(config);

  const handleConfigChange = (field: keyof AnalysisConfig, value: any) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">分析功能测试</h1>

        {/* 配置面板 */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">分析配置</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">股票代码</label>
              <input
                type="text"
                value={config.ticker}
                onChange={(e) => handleConfigChange('ticker', e.target.value.toUpperCase())}
                className="w-full px-3 py-2 border rounded-lg"
                placeholder="如 AAPL, TSLA, NVDA"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">分析日期</label>
              <input
                type="date"
                value={config.analysisDate}
                onChange={(e) => handleConfigChange('analysisDate', e.target.value)}
                className="w-full px-3 py-2 border rounded-lg"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">分析类型</label>
              <select
                value={config.analysisType}
                onChange={(e) => handleConfigChange('analysisType', e.target.value)}
                className="w-full px-3 py-2 border rounded-lg"
              >
                <option value="basic">基础分析</option>
                <option value="comprehensive">综合分析</option>
                <option value="quick">快速分析</option>
              </select>
            </div>
            
            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={config.includeRisk}
                  onChange={(e) => handleConfigChange('includeRisk', e.target.checked)}
                  className="mr-2"
                />
                风险评估
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={config.includeSentiment}
                  onChange={(e) => handleConfigChange('includeSentiment', e.target.checked)}
                  className="mr-2"
                />
                情绪分析
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={config.includeNews}
                  onChange={(e) => handleConfigChange('includeNews', e.target.checked)}
                  className="mr-2"
                />
                新闻分析
              </label>
            </div>
          </div>
        </div>

        {/* 控制面板 */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">控制面板</h2>
          <div className="flex gap-4">
            <button
              onClick={startAnalysis}
              disabled={isAnalyzing}
              className="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {isAnalyzing ? '分析中...' : '开始分析'}
            </button>
            
            <button
              onClick={stopAnalysis}
              disabled={!isAnalyzing}
              className="bg-red-500 text-white px-6 py-2 rounded hover:bg-red-600 disabled:opacity-50"
            >
              停止分析
            </button>
            
            <button
              onClick={restartAnalysis}
              disabled={isAnalyzing}
              className="bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600 disabled:opacity-50"
            >
              重新开始
            </button>
          </div>
          
          {analysisId && (
            <div className="mt-4 p-3 bg-gray-100 rounded">
              <p className="text-sm">分析ID: <code>{analysisId}</code></p>
            </div>
          )}
        </div>

        {/* 分析状态 */}
        {isAnalyzing && (
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">分析状态</h2>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span>当前阶段: {analysisState.currentStage}</span>
                  <span>{analysisState.progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${analysisState.progress}%` }}
                  />
                </div>
              </div>
              
              {analysisState.isComplete && (
                <div className="text-green-600 font-medium">
                  ✅ 分析完成！
                </div>
              )}
            </div>
          </div>
        )}

        {/* 代理状态 */}
        {agentStatuses.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">代理状态</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {agentStatuses.map((agent) => (
                <div key={agent.id} className="border rounded-lg p-4">
                  <h3 className="font-medium">{agent.name}</h3>
                  <div className="mt-2">
                    <div className="flex justify-between text-sm">
                      <span>状态: {agent.status}</span>
                      <span>{agent.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                      <div 
                        className="bg-green-500 h-1 rounded-full transition-all duration-300"
                        style={{ width: `${agent.progress}%` }}
                      />
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 mt-2">
                    最后更新: {new Date(agent.lastUpdate).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 分析报告 */}
        {reports.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">分析报告</h2>
            <div className="space-y-4">
              {reports.map((report) => (
                <div key={report.id} className="border rounded-lg p-4">
                  <h3 className="font-medium">{report.title}</h3>
                  <p className="text-gray-600 mt-2">{report.content}</p>
                  <div className="flex justify-between items-center mt-3 text-sm text-gray-500">
                    <span>置信度: {(report.confidence * 100).toFixed(1)}%</span>
                    <span>{new Date(report.timestamp).toLocaleString()}</span>
                  </div>
                  {report.recommendations && (
                    <div className="mt-3">
                      <h4 className="text-sm font-medium">建议:</h4>
                      <ul className="text-sm text-gray-600 mt-1">
                        {report.recommendations.map((rec, index) => (
                          <li key={index} className="ml-4">• {rec}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 最终决策 */}
        {finalDecision && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">交易决策</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium mb-2">决策信息</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>操作:</span>
                    <span className={`font-medium ${
                      finalDecision.action === 'buy' ? 'text-green-600' :
                      finalDecision.action === 'sell' ? 'text-red-600' :
                      'text-yellow-600'
                    }`}>
                      {finalDecision.action === 'buy' ? '买入' :
                       finalDecision.action === 'sell' ? '卖出' : '持有'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>置信度:</span>
                    <span>{(finalDecision.confidence * 100).toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>风险等级:</span>
                    <span className={`${
                      finalDecision.riskLevel === 'low' ? 'text-green-600' :
                      finalDecision.riskLevel === 'high' ? 'text-red-600' :
                      'text-yellow-600'
                    }`}>
                      {finalDecision.riskLevel === 'low' ? '低' :
                       finalDecision.riskLevel === 'high' ? '高' : '中'}
                    </span>
                  </div>
                  {finalDecision.targetPrice && (
                    <div className="flex justify-between">
                      <span>目标价格:</span>
                      <span>${finalDecision.targetPrice.toFixed(2)}</span>
                    </div>
                  )}
                  {finalDecision.stopLoss && (
                    <div className="flex justify-between">
                      <span>止损价格:</span>
                      <span>${finalDecision.stopLoss.toFixed(2)}</span>
                    </div>
                  )}
                </div>
              </div>
              
              <div>
                <h3 className="font-medium mb-2">决策理由</h3>
                <p className="text-sm text-gray-600">{finalDecision.reasoning}</p>
                <div className="text-xs text-gray-500 mt-3">
                  决策时间: {new Date(finalDecision.timestamp).toLocaleString()}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
