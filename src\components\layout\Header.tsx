'use client';

import { motion } from 'framer-motion';
import { ArrowLeftIcon, ChartBarIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';

interface HeaderProps {
  onBackToWelcome?: () => void;
}

export function Header({ onBackToWelcome }: HeaderProps) {
  return (
    <motion.header
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-b border-slate-200 dark:border-slate-700 sticky top-0 z-50"
    >
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {onBackToWelcome && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onBackToWelcome}
                className="flex items-center space-x-2"
              >
                <ArrowLeftIcon className="h-4 w-4" />
                <span>返回首页</span>
              </Button>
            )}
            
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
                <ChartBarIcon className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-slate-900 dark:text-white">
                  TradingAgents
                </h1>
                <p className="text-sm text-slate-600 dark:text-slate-400">
                  多智能体大语言模型金融交易框架
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="hidden md:flex items-center space-x-2 text-sm text-slate-600 dark:text-slate-400">
              <span>由</span>
              <a
                href="https://github.com/TauricResearch"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 hover:underline"
              >
                Tauric Research
              </a>
              <span>构建</span>
            </div>
          </div>
        </div>
      </div>
    </motion.header>
  );
}
