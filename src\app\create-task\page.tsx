'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeftIcon,
  PlayIcon,
  CheckIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  SparklesIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { format } from 'date-fns';

interface AnalystOption {
  id: string;
  name: string;
  description: string;
  icon: string;
}

interface ResearchDepthOption {
  value: string;
  label: string;
  description: string;
  icon: string;
}

export default function CreateTaskPage() {
  const router = useRouter();
  const [ticker, setTicker] = useState('');
  const [analysisPeriod, setAnalysisPeriod] = useState('');
  const [customStartDate, setCustomStartDate] = useState('');
  const [customEndDate, setCustomEndDate] = useState('');
  const [dateError, setDateError] = useState('');
  const [selectedAnalysts, setSelectedAnalysts] = useState<string[]>([]);
  const [researchDepth, setResearchDepth] = useState('');
  const [selectedDepthIndex, setSelectedDepthIndex] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  const analystOptions: AnalystOption[] = [
    { 
      id: 'market', 
      name: '市场分析师', 
      description: '技术指标和价格走势分析',
      icon: '📈'
    },
    { 
      id: 'social', 
      name: '社交媒体分析师', 
      description: '社交媒体情绪和舆论分析',
      icon: '💬'
    },
    { 
      id: 'news', 
      name: '新闻分析师', 
      description: '新闻事件和宏观经济分析',
      icon: '📰'
    },
    { 
      id: 'fundamentals', 
      name: '基本面分析师', 
      description: '财务报表和公司基本面分析',
      icon: '📊'
    },
  ];

  const analysisPeriodOptions = [
    { value: '1d', label: '1天', description: '最近1个交易日的数据分析' },
    { value: '1w', label: '1周', description: '最近1周的市场趋势分析' },
    { value: '1m', label: '1个月', description: '最近1个月的综合分析' },
    { value: '3m', label: '3个月', description: '最近3个月的中期趋势分析' },
    { value: '6m', label: '6个月', description: '最近6个月的长期趋势分析' },
    { value: '1y', label: '1年', description: '最近1年的全面历史分析' },
    { value: 'custom', label: '自定义', description: '选择特定的时间范围' },
  ];

  const researchDepthOptions: ResearchDepthOption[] = [
    {
      value: 'shallow',
      label: '浅层',
      description: '快速研究，少量辩论和策略讨论轮次',
      icon: '⚡'
    },
    {
      value: 'medium',
      label: '中等',
      description: '中等程度，适度的辩论轮次和策略讨论',
      icon: '⚖️'
    },
    {
      value: 'deep',
      label: '深层',
      description: '全面研究，深入的辩论和策略讨论',
      icon: '🔬'
    },
  ];

  // 日期验证函数
  const validateCustomDates = (startDate: string, endDate: string) => {
    if (!startDate || !endDate) {
      setDateError('');
      return true;
    }

    const start = new Date(startDate);
    const end = new Date(endDate);
    const today = new Date();

    // 检查开始时间不能晚于结束时间
    if (start > end) {
      setDateError('开始时间不能晚于结束时间');
      return false;
    }

    // 检查时间范围不能超过一年
    const oneYearInMs = 365 * 24 * 60 * 60 * 1000;
    if (end.getTime() - start.getTime() > oneYearInMs) {
      setDateError('时间范围不能超过一年');
      return false;
    }

    // 检查结束时间不能超过今天
    if (end > today) {
      setDateError('结束时间不能超过今天');
      return false;
    }

    setDateError('');
    return true;
  };

  // 监听自定义日期变化
  useEffect(() => {
    if (analysisPeriod === 'custom') {
      validateCustomDates(customStartDate, customEndDate);
    }
  }, [customStartDate, customEndDate, analysisPeriod]);

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // 分析师选择区域的键盘事件
      if (event.target && (event.target as HTMLElement).closest('.analyst-selection')) {
        if (event.code === 'Space') {
          event.preventDefault();
          // 这里需要知道当前焦点在哪个分析师上，暂时跳过具体实现
        } else if (event.key === 'a' || event.key === 'A') {
          event.preventDefault();
          toggleAllAnalysts();
        }
      }
      
      // 研究深度选择的方向键导航
      if (event.target && (event.target as HTMLElement).closest('.depth-selection')) {
        if (event.key === 'ArrowUp') {
          event.preventDefault();
          setSelectedDepthIndex(prev => Math.max(0, prev - 1));
        } else if (event.key === 'ArrowDown') {
          event.preventDefault();
          setSelectedDepthIndex(prev => Math.min(researchDepthOptions.length - 1, prev + 1));
        } else if (event.key === 'Enter') {
          event.preventDefault();
          setResearchDepth(researchDepthOptions[selectedDepthIndex].value);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedDepthIndex]);

  const toggleAnalyst = (analystId: string) => {
    setSelectedAnalysts(prev => 
      prev.includes(analystId)
        ? prev.filter(id => id !== analystId)
        : [...prev, analystId]
    );
  };

  const toggleAllAnalysts = () => {
    if (selectedAnalysts.length === analystOptions.length) {
      setSelectedAnalysts([]);
    } else {
      setSelectedAnalysts(analystOptions.map(option => option.id));
    }
  };

  const handleSubmit = async () => {
    // 基础验证
    if (!ticker || !analysisPeriod || selectedAnalysts.length === 0 || !researchDepth) {
      return;
    }

    // 自定义日期验证
    if (analysisPeriod === 'custom') {
      if (!customStartDate || !customEndDate) {
        setDateError('请选择开始和结束日期');
        return;
      }
      if (!validateCustomDates(customStartDate, customEndDate)) {
        return;
      }
    }

    setIsSubmitting(true);

    try {
      const config = {
        ticker: ticker.toUpperCase(),
        analysisPeriod,
        customStartDate: analysisPeriod === 'custom' ? customStartDate : undefined,
        customEndDate: analysisPeriod === 'custom' ? customEndDate : undefined,
        selectedAnalysts,
        researchDepth,
        timestamp: new Date().toISOString(),
      };

      // 发送创建任务请求
      const response = await fetch('/api/langgraph/analysis/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      if (response.ok) {
        const result = await response.json();
        setShowSuccess(true);
        
        // 2秒后跳转到分析页面
        setTimeout(() => {
          router.push(`/analysis/${result.analysisId}`);
        }, 2000);
      } else {
        throw new Error('创建任务失败');
      }
    } catch (error) {
      console.error('创建任务失败:', error);
      alert('创建任务失败，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  const isFormValid = ticker &&
    analysisPeriod &&
    selectedAnalysts.length > 0 &&
    researchDepth &&
    (analysisPeriod !== 'custom' || (customStartDate && customEndDate && !dateError));

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-900">
      <div className="container mx-auto px-4 py-8">
        {/* 返回按钮 */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="mb-8"
        >
          <Button variant="ghost" onClick={() => router.push('/')}>
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            返回首页
          </Button>
        </motion.div>

        {/* 页面标题 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-5xl font-bold text-slate-900 dark:text-white mb-4">
            创建分析任务
          </h1>
          <p className="text-xl text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
            配置您的智能交易分析参数，让AI分析师团队为您提供专业的投资建议
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto space-y-8">
          {/* 基础信息 */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>📋</span>
                  <span>基础信息</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex flex-row gap-6">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                      股票代码 *
                    </label>
                    <input
                      type="text"
                      value={ticker}
                      onChange={(e) => setTicker(e.target.value.toUpperCase())}
                      className="w-full px-4 py-3 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                      placeholder="例如: AAPL, TSLA, NVDA"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">
                      分析周期 *
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {analysisPeriodOptions.map((option) => (
                        <motion.div
                          key={option.value}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          className={`p-3 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                            analysisPeriod === option.value
                              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-md'
                              : 'border-slate-200 dark:border-slate-600 hover:border-blue-300 hover:shadow-sm'
                          }`}
                          onClick={() => setAnalysisPeriod(option.value)}
                        >
                          <div className="text-center">
                            <div className={`text-lg font-semibold mb-1 ${
                              analysisPeriod === option.value
                                ? 'text-blue-600 dark:text-blue-400'
                                : 'text-slate-900 dark:text-white'
                            }`}>
                              {option.label}
                            </div>
                            <div className="text-xs text-slate-600 dark:text-slate-400">
                              {option.description}
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>

                    {/* 自定义日期选择器 */}
                    <AnimatePresence>
                      {analysisPeriod === 'custom' && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
                        >
                          <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">
                            自定义时间范围
                          </h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label className="block text-xs font-medium text-slate-600 dark:text-slate-400 mb-1">
                                开始日期 *
                              </label>
                              <input
                                type="date"
                                value={customStartDate}
                                onChange={(e) => setCustomStartDate(e.target.value)}
                                max={format(new Date(), 'yyyy-MM-dd')}
                                className="w-full px-3 py-2 text-sm border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                              />
                            </div>
                            <div>
                              <label className="block text-xs font-medium text-slate-600 dark:text-slate-400 mb-1">
                                结束日期 *
                              </label>
                              <input
                                type="date"
                                value={customEndDate}
                                onChange={(e) => setCustomEndDate(e.target.value)}
                                min={customStartDate}
                                max={format(new Date(), 'yyyy-MM-dd')}
                                className="w-full px-3 py-2 text-sm border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                              />
                            </div>
                          </div>

                          {/* 日期验证提示 */}
                          {dateError && (
                            <motion.div
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
                            >
                              <p className="text-xs text-red-600 dark:text-red-400 flex items-center">
                                <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                                {dateError}
                              </p>
                            </motion.div>
                          )}

                          {/* 日期范围提示 */}
                          {customStartDate && customEndDate && !dateError && (
                            <motion.div
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-3 p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg"
                            >
                              <p className="text-xs text-green-600 dark:text-green-400 flex items-center">
                                <CheckIcon className="h-4 w-4 mr-1" />
                                时间范围: {Math.ceil((new Date(customEndDate).getTime() - new Date(customStartDate).getTime()) / (1000 * 60 * 60 * 24))} 天
                              </p>
                            </motion.div>
                          )}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* 分析师团队选择 */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="analyst-selection"
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span>👥</span>
                    <span>选择您的分析师团队</span>
                  </div>
                  <div className="text-sm text-slate-500 dark:text-slate-400">
                    已选择 {selectedAnalysts.length}/{analystOptions.length}
                  </div>
                </CardTitle>
                <div className="text-sm text-slate-600 dark:text-slate-400 space-y-1">
                  <p>• 按空格键选择/取消选择分析师</p>
                  <p>• 按 'a' 键全选/全不选</p>
                  <p>• 完成后按回车键</p>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {analystOptions.map((analyst, index) => (
                    <motion.div
                      key={analyst.id}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.1 * index }}
                      className={`p-4 border-2 rounded-xl cursor-pointer transition-all duration-300 ${
                        selectedAnalysts.includes(analyst.id)
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-lg transform scale-105'
                          : 'border-slate-200 dark:border-slate-600 hover:border-blue-300 hover:shadow-md'
                      }`}
                      onClick={() => toggleAnalyst(analyst.id)}
                      tabIndex={0}
                      onKeyDown={(e) => {
                        if (e.code === 'Space') {
                          e.preventDefault();
                          toggleAnalyst(analyst.id);
                        }
                      }}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0">
                          <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all ${
                            selectedAnalysts.includes(analyst.id)
                              ? 'border-blue-500 bg-blue-500'
                              : 'border-slate-300 dark:border-slate-600'
                          }`}>
                            {selectedAnalysts.includes(analyst.id) && (
                              <CheckIcon className="h-4 w-4 text-white" />
                            )}
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="text-lg">{analyst.icon}</span>
                            <h4 className="font-semibold text-slate-900 dark:text-white">
                              {analyst.name}
                            </h4>
                          </div>
                          <p className="text-sm text-slate-600 dark:text-slate-400">
                            {analyst.description}
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>

                <div className="mt-6 flex justify-center">
                  <Button
                    variant="ghost"
                    onClick={toggleAllAnalysts}
                    className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                  >
                    {selectedAnalysts.length === analystOptions.length ? '全不选' : '全选'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* 研究深度选择 */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="depth-selection"
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>🔍</span>
                  <span>选择您的研究深度</span>
                </CardTitle>
                <div className="text-sm text-slate-600 dark:text-slate-400 space-y-1">
                  <p>• 使用方向键导航</p>
                  <p>• 按回车键选择</p>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {researchDepthOptions.map((option, index) => (
                    <motion.div
                      key={option.value}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 * index }}
                      className={`p-6 border-2 rounded-xl cursor-pointer transition-all duration-300 ${
                        researchDepth === option.value
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-lg'
                          : selectedDepthIndex === index
                          ? 'border-blue-300 bg-blue-25 dark:bg-blue-900/10 shadow-md'
                          : 'border-slate-200 dark:border-slate-600 hover:border-slate-300'
                      }`}
                      onClick={() => setResearchDepth(option.value)}
                      tabIndex={0}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          setResearchDepth(option.value);
                        }
                      }}
                    >
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all ${
                            researchDepth === option.value
                              ? 'border-blue-500 bg-blue-500'
                              : 'border-slate-300 dark:border-slate-600'
                          }`}>
                            {researchDepth === option.value ? (
                              <CheckIcon className="h-5 w-5 text-white" />
                            ) : (
                              <span className="text-lg">{option.icon}</span>
                            )}
                          </div>
                        </div>
                        <div className="flex-1">
                          <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-1">
                            {option.label} - {option.description.split('，')[0]}
                          </h4>
                          <p className="text-sm text-slate-600 dark:text-slate-400">
                            {option.description}
                          </p>
                        </div>
                        {selectedDepthIndex === index && researchDepth !== option.value && (
                          <div className="flex-shrink-0">
                            <div className="flex flex-col space-y-1">
                              <ChevronUpIcon className="h-4 w-4 text-blue-500" />
                              <ChevronDownIcon className="h-4 w-4 text-blue-500" />
                            </div>
                          </div>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* 提交按钮 */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="flex justify-center"
          >
            <Button
              onClick={handleSubmit}
              disabled={!isFormValid || isSubmitting}
              size="lg"
              className={`px-12 py-4 text-lg font-semibold transition-all duration-300 ${
                isFormValid && !isSubmitting
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transform hover:scale-105 shadow-lg hover:shadow-xl'
                  : 'bg-slate-400 cursor-not-allowed'
              }`}
            >
              <div className="flex items-center space-x-3">
                {isSubmitting ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>创建中...</span>
                  </>
                ) : (
                  <>
                    <SparklesIcon className="h-6 w-6" />
                    <span>开始分析</span>
                    <PlayIcon className="h-6 w-6" />
                  </>
                )}
              </div>
            </Button>
          </motion.div>

          {/* 表单验证提示 */}
          {!isFormValid && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center text-sm text-slate-500 dark:text-slate-400"
            >
              <p>请完成所有必填项目：</p>
              <div className="flex justify-center space-x-4 mt-2">
                {!ticker && <span className="text-red-500">• 股票代码</span>}
                {!analysisPeriod && <span className="text-red-500">• 分析周期</span>}
                {analysisPeriod === 'custom' && !customStartDate && <span className="text-red-500">• 开始日期</span>}
                {analysisPeriod === 'custom' && !customEndDate && <span className="text-red-500">• 结束日期</span>}
                {analysisPeriod === 'custom' && dateError && <span className="text-red-500">• {dateError}</span>}
                {selectedAnalysts.length === 0 && <span className="text-red-500">• 分析师团队</span>}
                {!researchDepth && <span className="text-red-500">• 研究深度</span>}
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* 成功提交动画 */}
      <AnimatePresence>
        {showSuccess && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
          >
            <motion.div
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.5, opacity: 0 }}
              className="bg-white dark:bg-slate-800 rounded-2xl p-8 text-center max-w-md mx-4"
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <CheckIcon className="h-8 w-8 text-white" />
              </motion.div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">
                任务创建成功！
              </h3>
              <p className="text-slate-600 dark:text-slate-400 mb-4">
                正在启动AI分析师团队...
              </p>
              <div className="flex justify-center">
                <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
