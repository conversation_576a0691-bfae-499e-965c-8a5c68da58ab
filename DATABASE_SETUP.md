# 数据库连接问题解决方案

## 问题描述
当前遇到的错误：`ER_NOT_SUPPORTED_AUTH_MODE: Client does not support authentication protocol requested by server`

这是因为MySQL 8.0+ 默认使用 `caching_sha2_password` 认证插件，而一些较老的客户端不支持这种认证方式。

## 解决方案

### 方案1：修改用户认证方式（推荐）

```sql
-- 连接到MySQL服务器后执行以下命令
ALTER USER 'your_username'@'localhost' IDENTIFIED WITH mysql_native_password BY 'your_password';
FLUSH PRIVILEGES;
```

### 方案2：创建新用户使用兼容认证方式

```sql
-- 创建新用户
CREATE USER 'trading_user'@'localhost' IDENTIFIED WITH mysql_native_password BY 'trading_password';

-- 授予权限
GRANT ALL PRIVILEGES ON trading_analysis.* TO 'trading_user'@'localhost';
FLUSH PRIVILEGES;
```

### 方案3：修改MySQL配置文件

在 `my.cnf` 或 `my.ini` 文件中添加：

```ini
[mysqld]
default_authentication_plugin=mysql_native_password
```

然后重启MySQL服务。

### 方案4：使用MySQL命令行工具

```bash
# 使用MySQL命令行工具执行SQL文件
mysql -u username -p < database/schema.sql
```

## 执行步骤

1. **首先解决认证问题**（选择上述方案之一）

2. **执行数据库创建脚本**：
```bash
mysql -u username -p -e "CREATE DATABASE IF NOT EXISTS trading_analysis;"
```

3. **选择数据库**：
```bash
mysql -u username -p trading_analysis < database/schema.sql
```

4. **验证创建结果**：
```sql
USE trading_analysis;
SHOW TABLES;
```

## 预期结果

执行成功后，您应该看到以下表：
- tasks
- conversations  
- messages
- tool_calls
- analysis_results
- analysis_steps
- system_logs

以及视图：
- task_overview

## 测试连接

创建一个简单的连接测试脚本：

```javascript
// test-db-connection.js
const mysql = require('mysql2/promise');

async function testConnection() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'your_username',
      password: 'your_password',
      database: 'trading_analysis'
    });
    
    console.log('数据库连接成功！');
    
    const [rows] = await connection.execute('SHOW TABLES');
    console.log('表列表:', rows);
    
    await connection.end();
  } catch (error) {
    console.error('连接失败:', error.message);
  }
}

testConnection();
```
