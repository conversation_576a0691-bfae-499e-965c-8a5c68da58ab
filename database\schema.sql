-- 交易分析任务全流程记录数据库设计
-- 创建时间: 2025-07-05

CREATE DATABASE IF NOT EXISTS trading_analysis;
USE trading_analysis;

-- 1. 任务表 - 记录每个分析任务的基本信息
-- 注意：tasks 表的创建和修改现在通过 migration.sql 脚本处理。

-- 2. 对话会话表 - 记录任务中的对话会话
CREATE TABLE IF NOT EXISTS conversations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    conversation_id VARCHAR(36) UNIQUE NOT NULL COMMENT '会话唯一标识',
    task_id VARCHAR(36) NOT NULL COMMENT '关联任务ID',
    thread_id VARCHAR(100) COMMENT 'LangGraph线程ID',
    title VARCHAR(255) COMMENT '会话标题',
    status ENUM('active', 'completed', 'terminated') DEFAULT 'active' COMMENT '会话状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_message_at TIMESTAMP NULL COMMENT '最后消息时间',
    FOREIGN KEY (task_id) REFERENCES tasks(task_id) ON DELETE CASCADE,
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_task_id (task_id),
    INDEX idx_thread_id (thread_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='对话会话表';

-- 3. 消息表 - 记录具体的对话消息
CREATE TABLE IF NOT EXISTS messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    message_id VARCHAR(36) UNIQUE NOT NULL COMMENT '消息唯一标识',
    conversation_id VARCHAR(36) NOT NULL COMMENT '关联会话ID',
    task_id VARCHAR(36) NOT NULL COMMENT '关联任务ID',
    message_type ENUM('human', 'ai', 'system', 'tool') NOT NULL COMMENT '消息类型',
    content TEXT NOT NULL COMMENT '消息内容',
    metadata JSON COMMENT '消息元数据',
    sequence_number INT NOT NULL COMMENT '消息序号',
    parent_message_id VARCHAR(36) COMMENT '父消息ID（用于构建对话树）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES tasks(task_id) ON DELETE CASCADE,
    INDEX idx_message_id (message_id),
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_task_id (task_id),
    INDEX idx_message_type (message_type),
    INDEX idx_sequence_number (sequence_number),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息表';

-- 4. 工具调用表 - 记录分析过程中的工具调用
CREATE TABLE IF NOT EXISTS tool_calls (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tool_call_id VARCHAR(36) UNIQUE NOT NULL COMMENT '工具调用唯一标识',
    message_id VARCHAR(36) COMMENT '关联消息ID',
    conversation_id VARCHAR(36) NOT NULL COMMENT '关联会话ID',
    task_id VARCHAR(36) NOT NULL COMMENT '关联任务ID',
    tool_name VARCHAR(100) NOT NULL COMMENT '工具名称',
    tool_function VARCHAR(100) NOT NULL COMMENT '工具函数',
    input_parameters JSON COMMENT '输入参数',
    output_result JSON COMMENT '输出结果',
    status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending' COMMENT '调用状态',
    execution_time_ms INT COMMENT '执行时间(毫秒)',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    FOREIGN KEY (message_id) REFERENCES messages(message_id) ON DELETE SET NULL,
    FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES tasks(task_id) ON DELETE CASCADE,
    INDEX idx_tool_call_id (tool_call_id),
    INDEX idx_message_id (message_id),
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_task_id (task_id),
    INDEX idx_tool_name (tool_name),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工具调用表';

-- 5. 分析结果表 - 记录最终的分析结果
CREATE TABLE IF NOT EXISTS analysis_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    result_id VARCHAR(36) UNIQUE NOT NULL COMMENT '结果唯一标识',
    task_id VARCHAR(36) NOT NULL COMMENT '关联任务ID',
    conversation_id VARCHAR(36) COMMENT '关联会话ID',
    result_type ENUM('fundamental', 'technical', 'sentiment', 'decision', 'risk', 'comprehensive') NOT NULL COMMENT '结果类型',
    result_data JSON NOT NULL COMMENT '分析结果数据',
    confidence_score DECIMAL(5,4) COMMENT '置信度得分(0-1)',
    summary TEXT COMMENT '结果摘要',
    recommendations JSON COMMENT '建议列表',
    risk_level ENUM('low', 'medium', 'high', 'very_high') COMMENT '风险等级',
    version INT DEFAULT 1 COMMENT '结果版本',
    is_final BOOLEAN DEFAULT FALSE COMMENT '是否最终结果',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(task_id) ON DELETE CASCADE,
    FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id) ON DELETE SET NULL,
    INDEX idx_result_id (result_id),
    INDEX idx_task_id (task_id),
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_result_type (result_type),
    INDEX idx_is_final (is_final),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分析结果表';

-- 6. 分析步骤表 - 记录分析过程中的详细步骤
CREATE TABLE IF NOT EXISTS analysis_steps (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    step_id VARCHAR(36) UNIQUE NOT NULL COMMENT '步骤唯一标识',
    task_id VARCHAR(36) NOT NULL COMMENT '关联任务ID',
    conversation_id VARCHAR(36) COMMENT '关联会话ID',
    step_name VARCHAR(100) NOT NULL COMMENT '步骤名称',
    step_type ENUM('data_collection', 'analysis', 'processing', 'validation', 'decision') NOT NULL COMMENT '步骤类型',
    status ENUM('pending', 'running', 'completed', 'failed', 'skipped') DEFAULT 'pending' COMMENT '步骤状态',
    progress DECIMAL(5,2) DEFAULT 0.00 COMMENT '进度百分比(0-100)',
    description TEXT COMMENT '步骤描述',
    input_data JSON COMMENT '输入数据',
    output_data JSON COMMENT '输出数据',
    metrics JSON COMMENT '性能指标',
    duration_ms INT COMMENT '执行时长(毫秒)',
    sequence_order INT NOT NULL COMMENT '执行顺序',
    parent_step_id VARCHAR(36) COMMENT '父步骤ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    FOREIGN KEY (task_id) REFERENCES tasks(task_id) ON DELETE CASCADE,
    FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id) ON DELETE SET NULL,
    INDEX idx_step_id (step_id),
    INDEX idx_task_id (task_id),
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_step_type (step_type),
    INDEX idx_status (status),
    INDEX idx_sequence_order (sequence_order),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分析步骤表';

-- 7. 系统日志表 - 记录系统级别的操作日志
CREATE TABLE IF NOT EXISTS system_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    log_id VARCHAR(36) UNIQUE NOT NULL COMMENT '日志唯一标识',
    task_id VARCHAR(36) COMMENT '关联任务ID',
    log_level ENUM('DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL') NOT NULL COMMENT '日志级别',
    component VARCHAR(100) NOT NULL COMMENT '组件名称',
    operation VARCHAR(100) NOT NULL COMMENT '操作名称',
    message TEXT NOT NULL COMMENT '日志消息',
    details JSON COMMENT '详细信息',
    user_id VARCHAR(100) COMMENT '用户ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_log_id (log_id),
    INDEX idx_task_id (task_id),
    INDEX idx_log_level (log_level),
    INDEX idx_component (component),
    INDEX idx_operation (operation),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- 创建视图 - 任务概览视图
CREATE OR REPLACE VIEW task_overview AS
SELECT 
    t.task_id,
    t.ticker,
    t.title,
    t.status as task_status,
    t.created_at as task_created_at,
    t.completed_at as task_completed_at,
    COUNT(DISTINCT c.conversation_id) as conversation_count,
    COUNT(DISTINCT m.message_id) as message_count,
    COUNT(DISTINCT tc.tool_call_id) as tool_call_count,
    COUNT(DISTINCT ar.result_id) as result_count,
    MAX(m.created_at) as last_activity_at,
    TIMESTAMPDIFF(SECOND, t.created_at, COALESCE(t.completed_at, NOW())) as duration_seconds
FROM tasks t
LEFT JOIN conversations c ON t.task_id = c.task_id
LEFT JOIN messages m ON t.task_id = m.task_id
LEFT JOIN tool_calls tc ON t.task_id = tc.task_id
LEFT JOIN analysis_results ar ON t.task_id = ar.task_id
GROUP BY t.task_id, t.ticker, t.title, t.status, t.created_at, t.completed_at;

-- 创建存储过程 - 创建新任务
DROP PROCEDURE IF EXISTS CreateAnalysisTask;
DELIMITER //
CREATE PROCEDURE CreateAnalysisTask(
    IN p_task_id VARCHAR(36),
    IN p_ticker VARCHAR(20),
    IN p_title VARCHAR(255),
    IN p_description TEXT,
    IN p_config JSON,
    IN p_created_by VARCHAR(100),
    IN p_research_depth ENUM('shallow', 'medium', 'deep'),
    IN p_analysis_period VARCHAR(20)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    INSERT INTO tasks (task_id, ticker, title, description, config, created_by, status, research_depth, analysis_period)
    VALUES (p_task_id, p_ticker, p_title, p_description, p_config, p_created_by, 'pending', p_research_depth, p_analysis_period);
    
    COMMIT;
END //
DELIMITER ;

-- 创建存储过程 - 开始对话会话
DROP PROCEDURE IF EXISTS StartConversation;
DELIMITER //
CREATE PROCEDURE StartConversation(
    IN p_task_id VARCHAR(36),
    IN p_thread_id VARCHAR(100),
    IN p_title VARCHAR(255),
    OUT p_conversation_id VARCHAR(36)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    SET p_conversation_id = UUID();
    
    INSERT INTO conversations (conversation_id, task_id, thread_id, title)
    VALUES (p_conversation_id, p_task_id, p_thread_id, p_title);
    
    COMMIT;
END //
DELIMITER ;

-- 插入示例数据
INSERT INTO tasks (task_id, ticker, title, description, config, created_by) VALUES
(UUID(), 'AAPL', 'Apple股票分析', '分析Apple公司股票的投资价值', '{"analysis_type": "comprehensive", "time_horizon": "3_months"}', 'user1'),
(UUID(), 'TSLA', 'Tesla技术分析', '重点关注Tesla的技术面分析', '{"analysis_type": "technical", "indicators": ["RSI", "MACD", "MA"]}', 'user2'),
(UUID(), 'NVDA', 'NVIDIA基本面分析', '分析NVIDIA的基本面和未来前景', '{"analysis_type": "fundamental", "focus": ["earnings", "market_share"]}', 'user1');
