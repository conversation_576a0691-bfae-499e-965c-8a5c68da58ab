# 任务创建界面功能说明

## 概述

新的任务创建界面提供了一个直观、交互式的方式来配置AI交易分析任务。用户可以通过简单的点击和键盘操作来设置分析参数。

## 主要功能

### 1. 基础信息配置

#### 股票代码输入
- 支持输入任何股票代码（如 AAPL, TSLA, NVDA）
- 自动转换为大写
- 实时验证输入

#### 分析周期选择
- **1天**: 最近1个交易日的数据分析
- **1周**: 最近1周的市场趋势分析
- **1个月**: 最近1个月的综合分析
- **3个月**: 最近3个月的中期趋势分析
- **6个月**: 最近6个月的长期趋势分析
- **1年**: 最近1年的全面历史分析
- **自定义**: 选择特定的时间范围（支持自定义开始和结束日期）

#### 自定义时间范围功能
- **智能验证**: 自动验证日期范围的合理性
- **时间限制**:
  - 开始时间不能晚于结束时间
  - 时间范围不能超过一年（365天）
  - 结束时间不能超过今天
- **实时反馈**:
  - 实时显示验证错误信息
  - 显示选择的时间范围天数
  - 动态调整日期选择器的限制
- **用户体验**:
  - 平滑的动画展开/收起
  - 智能的日期输入限制
  - 清晰的视觉状态指示

### 2. 分析师团队选择

#### 可选分析师类型
- **📈 市场分析师**: 技术指标和价格走势分析
- **💬 社交媒体分析师**: 社交媒体情绪和舆论分析
- **📰 新闻分析师**: 新闻事件和宏观经济分析
- **📊 基本面分析师**: 财务报表和公司基本面分析

#### 交互方式
- **鼠标点击**: 直接点击分析师卡片进行选择/取消选择
- **空格键**: 在焦点分析师上按空格键进行选择/取消选择
- **'a' 键**: 全选/全不选所有分析师
- **回车键**: 确认当前选择

### 3. 研究深度选择

#### 深度级别
- **⚡ 浅层**: 快速研究，少量辩论和策略讨论轮次
- **⚖️ 中等**: 中等程度，适度的辩论轮次和策略讨论
- **🔬 深层**: 全面研究，深入的辩论和策略讨论

#### 交互方式
- **鼠标点击**: 直接点击选项进行选择
- **方向键**: 使用 ↑↓ 键导航选项
- **回车键**: 选择当前高亮的选项

### 4. 提交和反馈

#### 开始分析按钮
- 酷炫的渐变设计
- 动态悬停效果
- 提交时显示加载动画
- 表单验证提示

#### 成功反馈
- 动画确认对话框
- 自动跳转到分析页面
- 实时状态更新

## 技术特性

### 响应式设计
- 支持桌面和移动设备
- 自适应布局
- 流畅的动画效果

### 键盘导航
- 完整的键盘支持
- 无障碍访问
- 直观的快捷键

### 实时验证
- 表单字段实时验证
- 清晰的错误提示
- 智能提交控制

### 动画效果
- Framer Motion 驱动的流畅动画
- 微交互反馈
- 视觉层次清晰

## 使用流程

1. **访问页面**: 从首页点击"快速开始"按钮
2. **输入股票代码**: 在第一个输入框中输入要分析的股票代码
3. **选择分析周期**: 点击合适的时间周期选项
4. **选择分析师团队**: 
   - 点击需要的分析师类型
   - 或使用键盘快捷键进行批量选择
5. **选择研究深度**: 
   - 点击合适的深度级别
   - 或使用方向键导航选择
6. **提交任务**: 点击"开始分析"按钮
7. **查看结果**: 自动跳转到分析进度页面

## API 集成

### 创建任务

#### 预设周期
```javascript
POST /api/langgraph/analysis/start
{
  "ticker": "AAPL",
  "analysisPeriod": "1m",
  "selectedAnalysts": ["market", "fundamentals", "news"],
  "researchDepth": "medium",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### 自定义周期
```javascript
POST /api/langgraph/analysis/start
{
  "ticker": "AAPL",
  "analysisPeriod": "custom",
  "customStartDate": "2024-01-01",
  "customEndDate": "2024-03-31",
  "selectedAnalysts": ["market", "fundamentals", "news"],
  "researchDepth": "medium",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 查看状态
```javascript
GET /api/langgraph/analysis/status?id={analysisId}
```

## 文件结构

```
src/app/create-task/
├── page.tsx                 # 主要任务创建页面
└── ...

src/app/analysis/[id]/
├── page.tsx                 # 分析结果展示页面
└── ...

src/app/api/langgraph/analysis/
├── start/route.ts           # 启动分析API
├── status/route.ts          # 状态查询API
└── ...
```

## 下一步改进

1. **添加更多分析师类型**: 风险管理师、量化分析师等
2. **自定义分析参数**: 允许用户调整更详细的分析参数
3. **历史任务管理**: 查看和管理历史分析任务
4. **结果导出**: 支持PDF、Excel等格式导出
5. **实时通知**: WebSocket实时推送分析进度
