// 数据库API路由 - 任务管理
import { NextRequest, NextResponse } from 'next/server';
import { CreateTaskRequest } from '@/types/database';

// 模拟数据库操作（实际应该连接到真实的数据库）
// 这里使用内存存储作为示例，实际项目中应该使用MySQL连接

let tasks: any[] = [];
let conversations: any[] = [];
let messages: any[] = [];
let toolCalls: any[] = [];
let analysisResults: any[] = [];
let analysisSteps: any[] = [];

export async function POST(request: NextRequest) {
  try {
    const body: CreateTaskRequest = await request.json();
    
    const task = {
      id: tasks.length + 1,
      task_id: generateUUID(),
      ticker: body.ticker,
      title: body.title,
      description: body.description,
      status: 'pending',
      config: body.config,
      priority: 0,
      created_by: body.created_by,
      created_at: new Date(),
      updated_at: new Date(),
      started_at: null,
      completed_at: null,
      error_message: null,
    };
    
    tasks.push(task);
    
    return NextResponse.json({
      success: true,
      task_id: task.task_id,
      message: '任务创建成功',
    });
  } catch (error) {
    console.error('创建任务失败:', error);
    return NextResponse.json(
      { success: false, message: '创建任务失败' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const ticker = searchParams.get('ticker');
    const createdBy = searchParams.get('created_by');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    let filteredTasks = [...tasks];
    
    if (status) {
      const statusList = status.split(',');
      filteredTasks = filteredTasks.filter(task => statusList.includes(task.status));
    }
    
    if (ticker) {
      filteredTasks = filteredTasks.filter(task => task.ticker === ticker);
    }
    
    if (createdBy) {
      filteredTasks = filteredTasks.filter(task => task.created_by === createdBy);
    }
    
    // 分页
    const paginatedTasks = filteredTasks.slice(offset, offset + limit);
    
    return NextResponse.json(paginatedTasks);
  } catch (error) {
    console.error('查询任务失败:', error);
    return NextResponse.json(
      { message: '查询任务失败' },
      { status: 500 }
    );
  }
}

function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
