'use client';

import React, { useState } from 'react';
import { useLangGraphAgent } from '@/hooks/useLangGraphAgent';
import { langGraphClient } from '@/lib/langgraph-client';

export default function TestLangGraphPage() {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const {
    messages,
    isProcessing,
    isStreaming,
    currentStep,
    progress,
    connectionStatus,
    sessionInfo,
    threadId,
    analyzeStock,
    sendMessage,
    streamAnalysis,
    clearConversation,
  } = useLangGraphAgent();

  const addTestResult = (test: string, result: any, success: boolean) => {
    setTestResults(prev => [...prev, {
      test,
      result,
      success,
      timestamp: new Date().toISOString(),
    }]);
  };

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    try {
      // 测试 1: 直接 API 调用
      console.log('测试 1: 直接 API 调用');
      try {
        const directResult = await langGraphClient.sendMessage('测试消息');
        addTestResult('直接 API 调用', directResult, true);
      } catch (error) {
        addTestResult('直接 API 调用', error, false);
      }

      // 测试 2: Hook 发送消息
      console.log('测试 2: Hook 发送消息');
      try {
        await sendMessage('测试 Hook 消息');
        addTestResult('Hook 发送消息', '成功', true);
      } catch (error) {
        addTestResult('Hook 发送消息', error, false);
      }

      // 测试 3: 股票分析
      console.log('测试 3: 股票分析');
      try {
        const analysisResult = await analyzeStock('AAPL');
        addTestResult('股票分析', analysisResult, true);
      } catch (error) {
        addTestResult('股票分析', error, false);
      }

      // 测试 4: 流式分析
      console.log('测试 4: 流式分析');
      try {
        const streamResults: any[] = [];
        for await (const chunk of streamAnalysis('TSLA')) {
          streamResults.push(chunk);
          if (streamResults.length >= 3) break; // 只测试前几个块
        }
        addTestResult('流式分析', streamResults, true);
      } catch (error) {
        addTestResult('流式分析', error, false);
      }

      // 测试 5: WebSocket 信息获取
      console.log('测试 5: WebSocket 信息获取');
      try {
        const wsInfo = await langGraphClient.getWebSocketInfo('test-thread');
        addTestResult('WebSocket 信息', wsInfo, true);
      } catch (error) {
        addTestResult('WebSocket 信息', error, false);
      }

    } catch (error) {
      console.error('测试运行失败:', error);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">LangGraph 功能测试</h1>

        {/* 控制面板 */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">控制面板</h2>
          <div className="flex gap-4">
            <button
              onClick={runTests}
              disabled={isRunning}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {isRunning ? '运行中...' : '运行测试'}
            </button>
            <button
              onClick={clearConversation}
              className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
            >
              清除对话
            </button>
          </div>
        </div>

        {/* 状态显示 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="font-semibold mb-2">连接状态</h3>
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${
                connectionStatus === 'connected' ? 'bg-green-500' :
                connectionStatus === 'connecting' ? 'bg-yellow-500' :
                connectionStatus === 'error' ? 'bg-red-500' :
                'bg-gray-400'
              }`} />
              <span>{connectionStatus}</span>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="font-semibold mb-2">处理状态</h3>
            <div>
              {isProcessing && <span className="text-blue-600">处理中</span>}
              {isStreaming && <span className="text-purple-600">流式处理</span>}
              {!isProcessing && !isStreaming && <span className="text-gray-600">空闲</span>}
            </div>
            {currentStep && <div className="text-sm text-gray-500 mt-1">{currentStep}</div>}
          </div>

          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="font-semibold mb-2">会话信息</h3>
            <div className="text-sm">
              <div>线程ID: {threadId ? threadId.slice(-8) : '无'}</div>
              <div>消息数: {sessionInfo.messageCount}</div>
              {progress > 0 && <div>进度: {progress}%</div>}
            </div>
          </div>
        </div>

        {/* 测试结果 */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">测试结果</h2>
          {testResults.length === 0 ? (
            <p className="text-gray-500">点击"运行测试"开始测试</p>
          ) : (
            <div className="space-y-4">
              {testResults.map((result, index) => (
                <div key={index} className={`p-4 rounded border-l-4 ${
                  result.success ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'
                }`}>
                  <div className="flex justify-between items-start">
                    <h3 className="font-medium">{result.test}</h3>
                    <span className={`px-2 py-1 rounded text-sm ${
                      result.success ? 'bg-green-200 text-green-800' : 'bg-red-200 text-red-800'
                    }`}>
                      {result.success ? '成功' : '失败'}
                    </span>
                  </div>
                  <div className="mt-2 text-sm text-gray-600">
                    <pre className="whitespace-pre-wrap">
                      {typeof result.result === 'string' 
                        ? result.result 
                        : JSON.stringify(result.result, null, 2)
                      }
                    </pre>
                  </div>
                  <div className="text-xs text-gray-400 mt-2">
                    {new Date(result.timestamp).toLocaleString()}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 消息历史 */}
        {messages.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">消息历史</h2>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {messages.map((message) => (
                <div key={message.id} className={`p-3 rounded ${
                  message.type === 'human' ? 'bg-blue-50 ml-8' :
                  message.type === 'ai' ? 'bg-green-50 mr-8' :
                  'bg-gray-50'
                }`}>
                  <div className="text-xs text-gray-500 mb-1">
                    {message.type} - {message.timestamp.toLocaleTimeString()}
                  </div>
                  <div className="text-sm">{message.content}</div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
