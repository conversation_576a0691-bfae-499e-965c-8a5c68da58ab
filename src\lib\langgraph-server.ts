// 服务端专用的 LangGraph 模块
// 这个文件只在 Node.js 环境中运行

import { StateGraph, MemorySaver, Annotation, messagesStateReducer } from '@langchain/langgraph';
import { ToolNode } from '@langchain/langgraph/prebuilt';
import { ChatOpenAI } from '@langchain/openai';
import { HumanMessage, AIMessage, BaseMessage } from '@langchain/core/messages';
import { tool } from '@langchain/core/tools';
import { z } from 'zod';

// 导入事件发射器用于实时通信
import { EventEmitter } from 'events';

// 环境配置
const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY;
const OPENAI_BASE_URL = process.env.OPENAI_BASE_URL || process.env.NEXT_PUBLIC_OPENAI_BASE_URL || 'https://api.openai.com/v1';

// 交易分析工具定义
const stockAnalysisTool = tool(
  async ({ ticker, analysisType }) => {
    // 模拟股票分析数据
    const mockData = {
      ticker,
      analysisType,
      data: {
        price: Math.random() * 1000 + 100,
        change: (Math.random() - 0.5) * 10,
        volume: Math.floor(Math.random() * 1000000),
        recommendation: ['买入', '持有', '卖出'][Math.floor(Math.random() * 3)],
      },
      timestamp: new Date().toISOString(),
    };
    return JSON.stringify(mockData);
  },
  {
    name: 'stock_analysis',
    description: '分析股票的基本面、技术面或新闻情绪',
    schema: z.object({
      ticker: z.string().describe('股票代码，如NVDA、AAPL等'),
      analysisType: z.enum(['fundamentals', 'technical', 'news', 'sentiment']).describe('分析类型'),
    }),
  }
);

const marketDataTool = tool(
  async ({ ticker, dataType, period }) => {
    // 模拟市场数据
    const mockData = {
      ticker,
      dataType,
      period,
      data: Array.from({ length: 10 }, (_, i) => ({
        timestamp: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
        value: Math.random() * 100 + 50,
      })),
    };
    return JSON.stringify(mockData);
  },
  {
    name: 'market_data',
    description: '获取股票的市场数据，包括价格、成交量等',
    schema: z.object({
      ticker: z.string().describe('股票代码'),
      dataType: z.enum(['price', 'volume', 'indicators']).describe('数据类型'),
      period: z.enum(['1d', '1w', '1m', '3m', '1y']).describe('时间周期'),
    }),
  }
);

const newsAnalysisTool = tool(
  async ({ ticker, sentiment }) => {
    // 模拟新闻分析
    const mockData = {
      ticker,
      sentiment: sentiment ? 'positive' : 'neutral',
      news: [
        { title: `${ticker} 财报超预期`, sentiment: 'positive', impact: 'high' },
        { title: `市场对 ${ticker} 前景看好`, sentiment: 'positive', impact: 'medium' },
      ],
    };
    return JSON.stringify(mockData);
  },
  {
    name: 'news_analysis',
    description: '分析股票相关新闻的情绪和影响',
    schema: z.object({
      ticker: z.string().describe('股票代码'),
      sentiment: z.boolean().optional().describe('是否进行情绪分析'),
    }),
  }
);

const riskAssessmentTool = tool(
  async ({ ticker, portfolio, riskLevel }) => {
    // 模拟风险评估
    const mockData = {
      ticker,
      riskLevel,
      assessment: {
        volatility: Math.random() * 0.5,
        beta: Math.random() * 2,
        sharpeRatio: Math.random() * 3,
        recommendation: riskLevel === 'low' ? '适合保守投资者' : '适合激进投资者',
      },
    };
    return JSON.stringify(mockData);
  },
  {
    name: 'risk_assessment',
    description: '评估投资风险和组合风险',
    schema: z.object({
      ticker: z.string().describe('股票代码'),
      portfolio: z.array(z.string()).optional().describe('投资组合中的其他股票'),
      riskLevel: z.enum(['low', 'medium', 'high']).describe('风险偏好'),
    }),
  }
);

// 定义工具集合
const tools = [stockAnalysisTool, marketDataTool, newsAnalysisTool, riskAssessmentTool];

// 创建工具节点
const toolNode = new ToolNode(tools);

// 创建LLM模型
export function createTradingLLM(modelName: string = 'gpt-4o-mini', temperature: number = 0) {
  return new ChatOpenAI({
    modelName,
    temperature,
    openAIApiKey: OPENAI_API_KEY,
    configuration: {
      baseURL: OPENAI_BASE_URL,
    },
  }).bindTools(tools);
}

// 定义状态接口
export interface TradingAgentState {
  messages: BaseMessage[];
  ticker?: string;
  analysisConfig?: any;
  analysisResults?: any;
  tradingDecision?: any;
  riskAssessment?: any;
}

// 定义会话状态接口
export interface SessionState {
  threadId: string;
  messages: BaseMessage[];
  currentStep: string;
  isProcessing: boolean;
  analysisResults?: any;
  tradingDecision?: any;
  error?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// LangGraph 服务类
export class LangGraphService extends EventEmitter {
  private graph: StateGraph<TradingAgentState>;
  private memory: MemorySaver;
  private sessions: Map<string, SessionState> = new Map();
  private llm: ChatOpenAI;

  constructor() {
    super();
    this.memory = new MemorySaver();
    this.llm = this.createTradingLLM();
    this.graph = this.createTradingGraph();
  }

  // 创建 LLM 实例
  private createTradingLLM(modelName: string = 'gpt-4o-mini', temperature: number = 0): ChatOpenAI {
    return new ChatOpenAI({
      modelName,
      temperature,
      openAIApiKey: OPENAI_API_KEY,
      configuration: {
        baseURL: OPENAI_BASE_URL,
      },
    }).bindTools(tools);
  }

  // 创建交易分析图
  private createTradingGraph(): StateGraph<TradingAgentState> {
    // 定义状态注解
    const StateAnnotation = Annotation.Root({
      messages: Annotation<BaseMessage[]>({
        reducer: messagesStateReducer,
      }),
      ticker: Annotation<string>(),
      analysisConfig: Annotation<any>(),
      analysisResults: Annotation<any>(),
      tradingDecision: Annotation<any>(),
      riskAssessment: Annotation<any>(),
    });

    // 创建图
    const graph = new StateGraph(StateAnnotation)
      .addNode('agent', this.agentNode.bind(this))
      .addNode('tools', toolNode)
      .addEdge('__start__', 'agent')
      .addConditionalEdges('agent', this.shouldContinue.bind(this))
      .addEdge('tools', 'agent');

    return graph.compile({ checkpointer: this.memory });
  }
  // 代理节点处理函数
  private async agentNode(state: TradingAgentState): Promise<Partial<TradingAgentState>> {
    const response = await this.llm.invoke(state.messages);
    return { messages: [response] };
  }

  // 判断是否继续执行工具
  private shouldContinue(state: TradingAgentState): string {
    const messages = state.messages;
    const lastMessage = messages[messages.length - 1];

    if (lastMessage.additional_kwargs?.tool_calls) {
      return 'tools';
    }
    return '__end__';
  }

  // 创建新会话
  public createSession(threadId?: string): string {
    const sessionId = threadId || `thread_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const session: SessionState = {
      threadId: sessionId,
      messages: [],
      currentStep: '',
      isProcessing: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.sessions.set(sessionId, session);
    return sessionId;
  }

  // 获取会话状态
  public getSession(threadId: string): SessionState | null {
    return this.sessions.get(threadId) || null;
  }

  // 更新会话状态
  private updateSession(threadId: string, updates: Partial<SessionState>): void {
    const session = this.sessions.get(threadId);
    if (session) {
      Object.assign(session, updates, { updatedAt: new Date() });
      this.sessions.set(threadId, session);

      // 发射状态更新事件
      this.emit('sessionUpdate', { threadId, session });
    }
  }

  // 发送消息
  public async sendMessage(threadId: string, message: string): Promise<any> {
    let session = this.getSession(threadId);
    if (!session) {
      this.createSession(threadId);
      session = this.getSession(threadId)!;
    }

    try {
      this.updateSession(threadId, {
        isProcessing: true,
        currentStep: '处理消息...',
        error: null
      });

      // 添加用户消息
      const humanMessage = new HumanMessage(message);

      // 调用图执行
      const config = { configurable: { thread_id: threadId } };
      const result = await this.graph.invoke(
        { messages: [humanMessage] },
        config
      );

      // 更新会话状态
      this.updateSession(threadId, {
        isProcessing: false,
        currentStep: '完成',
        messages: result.messages,
      });

      return {
        content: result.messages[result.messages.length - 1].content,
        metadata: {
          threadId,
          timestamp: new Date().toISOString(),
          messageType: 'chat_response',
        },
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.updateSession(threadId, {
        isProcessing: false,
        currentStep: '处理失败',
        error: errorMessage,
      });
      throw error;
    }
  }

  // 分析股票
  public async analyzeStock(threadId: string, ticker: string, config: any = {}): Promise<any> {
    let session = this.getSession(threadId);
    if (!session) {
      this.createSession(threadId);
      session = this.getSession(threadId)!;
    }

    try {
      this.updateSession(threadId, {
        isProcessing: true,
        currentStep: '开始分析...',
        error: null,
      });

      // 创建分析消息
      const analysisMessage = new HumanMessage(`请分析股票 ${ticker}`);

      // 调用图执行
      const graphConfig = { configurable: { thread_id: threadId } };
      const result = await this.graph.invoke(
        {
          messages: [analysisMessage],
          ticker,
          analysisConfig: config,
        },
        graphConfig
      );

      // 更新会话状态
      this.updateSession(threadId, {
        isProcessing: false,
        currentStep: '分析完成',
        messages: result.messages,
        analysisResults: result.analysisResults,
        tradingDecision: result.tradingDecision,
      });

      return {
        content: result.messages[result.messages.length - 1].content,
        analysisResults: result.analysisResults,
        tradingDecision: result.tradingDecision,
        metadata: {
          threadId,
          ticker,
          timestamp: new Date().toISOString(),
          messageType: 'analysis_response',
        },
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.updateSession(threadId, {
        isProcessing: false,
        currentStep: '分析失败',
        error: errorMessage,
      });
      throw error;
    }
  }

  // 流式分析
  public async* streamAnalysis(threadId: string, ticker: string, config: any = {}): AsyncGenerator<any> {
    let session = this.getSession(threadId);
    if (!session) {
      this.createSession(threadId);
      session = this.getSession(threadId)!;
    }

    try {
      // 开始分析
      this.updateSession(threadId, {
        isProcessing: true,
        currentStep: '初始化分析...',
        error: null,
      });

      yield { currentStep: '初始化分析...', progress: 0 };

      // 模拟分析步骤
      const steps = [
        { step: '收集数据...', progress: 20 },
        { step: '基本面分析...', progress: 40 },
        { step: '技术分析...', progress: 60 },
        { step: '情绪分析...', progress: 80 },
        { step: '生成决策...', progress: 100 },
      ];

      for (const stepInfo of steps) {
        this.updateSession(threadId, { currentStep: stepInfo.step });
        yield stepInfo;
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // 执行最终分析
      const result = await this.analyzeStock(threadId, ticker, config);
      yield { ...result, progress: 100 };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.updateSession(threadId, {
        isProcessing: false,
        currentStep: '分析失败',
        error: errorMessage,
      });
      throw error;
    }
  }

  // 清除会话
  public clearSession(threadId: string): void {
    this.sessions.delete(threadId);
    this.emit('sessionCleared', { threadId });
  }

  // 获取所有活跃会话
  public getActiveSessions(): SessionState[] {
    return Array.from(this.sessions.values());
  }
}

// 创建全局服务实例
export const langGraphService = new LangGraphService();

// 创建交易代理状态注解
export const TradingAgentAnnotation = Annotation.Root({
  messages: Annotation({
    reducer: messagesStateReducer,
    default: () => [],
  }),
  ticker: Annotation({
    reducer: (x: string, y?: string) => y ?? x,
    default: () => '',
  }),
  analysisConfig: Annotation({
    reducer: (x: any, y?: any) => y ?? x,
    default: () => ({}),
  }),
  analysisResults: Annotation({
    reducer: (x: any, y?: any) => y ? { ...x, ...y } : x,
    default: () => ({}),
  }),
  tradingDecision: Annotation({
    reducer: (x: any, y?: any) => y ?? x,
    default: () => null,
  }),
  riskAssessment: Annotation({
    reducer: (x: any, y?: any) => y ?? x,
    default: () => null,
  }),
});

// 判断是否继续执行的函数
function shouldContinue(state: typeof TradingAgentAnnotation.State) {
  const lastMessage = state.messages[state.messages.length - 1] as AIMessage;

  // 如果LLM调用了工具，则路由到工具节点
  if (lastMessage.tool_calls?.length) {
    return 'tools';
  }

  // 否则结束执行
  return '__end__';
}

// 调用模型的函数
async function callModel(state: typeof TradingAgentAnnotation.State) {
  const model = createTradingLLM();

  // 构建系统提示
  const systemPrompt = `你是一个专业的金融交易分析师。你的任务是：
1. 分析股票的基本面、技术面和新闻情绪
2. 评估投资风险
3. 提供明确的交易建议

当前分析的股票代码是: ${state.ticker}

请使用可用的工具来收集和分析数据，然后提供专业的交易建议。`;

  const messages = [
    new HumanMessage(systemPrompt),
    ...state.messages,
  ];

  const response = await model.invoke(messages);

  return { messages: [response] };
}

// 创建交易分析工作流
export function createTradingWorkflow() {
  const workflow = new StateGraph(TradingAgentAnnotation)
    // 添加节点
    .addNode('agent', callModel)
    .addNode('tools', toolNode)

    // 添加边
    .addEdge('__start__', 'agent')
    .addEdge('tools', 'agent')
    .addConditionalEdges('agent', shouldContinue);

  // 添加内存保存器
  const checkpointer = new MemorySaver();

  return workflow.compile({ checkpointer });
}

// 简化的交易代理类
export class TradingAgent {
  private workflow: any;

  constructor() {
    this.workflow = createTradingWorkflow();
  }

  async analyze(ticker: string, config: any = {}) {
    const initialState = {
      messages: [new HumanMessage(`请分析股票 ${ticker}`)],
      ticker,
      analysisConfig: config,
    };

    const result = await this.workflow.invoke(initialState, {
      configurable: { thread_id: `analysis_${ticker}_${Date.now()}` }
    });

    return result;
  }

  async chat(message: string, threadId: string) {
    const result = await this.workflow.invoke(
      { messages: [new HumanMessage(message)] },
      { configurable: { thread_id: threadId } }
    );

    return result;
  }

  async getState(threadId: string) {
    return await this.workflow.getState({ configurable: { thread_id: threadId } });
  }
}

// 导出默认实例
export const tradingAgent = new TradingAgent();
