'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { TradingAgent } from '@/lib/langgraph';
import { taskFlowDb } from '@/lib/task-flow-database';
import { Task, TaskOverview } from '@/types/database';

export default function TaskFlowDemoPage() {
  const [tradingAgent] = useState(() => new TradingAgent());
  const [currentTask, setCurrentTask] = useState<Task | null>(null);
  const [taskOverview, setTaskOverview] = useState<TaskOverview | null>(null);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  // 添加日志
  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  // 加载任务列表
  const loadTasks = async () => {
    try {
      const taskList = await taskFlowDb.queryTasks({ limit: 10 });
      setTasks(taskList);
    } catch (error) {
      console.error('加载任务列表失败:', error);
      addLog('加载任务列表失败');
    }
  };

  // 创建新任务
  const createNewTask = async () => {
    try {
      setLoading(true);
      addLog('开始创建新任务...');

      const taskId = await tradingAgent.initializeTask(
        'AAPL',
        'Apple股票全面分析',
        '分析Apple公司的投资价值和风险',
        {
          analysis_type: 'comprehensive',
          time_horizon: '3_months',
          risk_tolerance: 'medium'
        }
      );

      addLog(`任务创建成功，ID: ${taskId}`);

      // 开始对话
      const conversationId = await tradingAgent.startConversation(
        undefined,
        'Apple股票分析对话'
      );
      
      addLog(`对话会话创建成功，ID: ${conversationId}`);

      // 获取任务信息
      const task = await taskFlowDb.getTask(taskId);
      setCurrentTask(task);

      // 刷新任务列表
      await loadTasks();

    } catch (error) {
      console.error('创建任务失败:', error);
      addLog('创建任务失败: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  // 执行分析
  const runAnalysis = async () => {
    if (!currentTask) {
      addLog('没有活跃的任务');
      return;
    }

    try {
      setLoading(true);
      addLog('开始执行股票分析...');

      const result = await tradingAgent.analyze('AAPL', {
        include_fundamental: true,
        include_technical: true,
        include_sentiment: true,
      });

      addLog('分析完成，结果已保存');
      addLog(`分析结果: ${JSON.stringify(result, null, 2)}`);

      // 更新任务概览
      await updateTaskOverview();

    } catch (error) {
      console.error('分析失败:', error);
      addLog('分析失败: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  // 模拟聊天对话
  const simulateChat = async () => {
    if (!currentTask) {
      addLog('没有活跃的任务');
      return;
    }

    try {
      setLoading(true);
      addLog('开始模拟对话...');

      const questions = [
        '请详细分析Apple的财务状况',
        'Apple相比竞争对手有什么优势？',
        '未来一个季度的投资建议是什么？'
      ];

      for (const question of questions) {
        addLog(`用户问题: ${question}`);
        
        const response = await tradingAgent.chat(question, 'demo-thread');
        addLog(`AI回复: ${JSON.stringify(response, null, 2)}`);
        
        // 短暂延迟模拟真实对话
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      addLog('对话模拟完成');
      await updateTaskOverview();

    } catch (error) {
      console.error('对话失败:', error);
      addLog('对话失败: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  // 完成任务
  const completeTask = async () => {
    if (!currentTask) {
      addLog('没有活跃的任务');
      return;
    }

    try {
      setLoading(true);
      addLog('完成任务...');

      await tradingAgent.completeTask();
      addLog('任务已完成');

      setCurrentTask(null);
      setTaskOverview(null);
      await loadTasks();

    } catch (error) {
      console.error('完成任务失败:', error);
      addLog('完成任务失败: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  // 更新任务概览
  const updateTaskOverview = async () => {
    if (!currentTask) return;

    try {
      const overview = await tradingAgent.getTaskOverview();
      setTaskOverview(overview);
    } catch (error) {
      console.error('获取任务概览失败:', error);
    }
  };

  // 查看任务历史
  const viewTaskHistory = async (taskId: string) => {
    try {
      addLog(`查看任务历史: ${taskId}`);
      const agent = new TradingAgent();
      const history = await agent.getTaskHistory(taskId);
      
      addLog('任务历史记录:');
      addLog(`- 对话数量: ${history.conversations.length}`);
      addLog(`- 消息数量: ${history.messages.length}`);
      addLog(`- 工具调用数量: ${history.toolCalls.length}`);
      addLog(`- 分析结果数量: ${history.analysisResults.length}`);
      addLog(`- 分析步骤数量: ${history.analysisSteps.length}`);
      
    } catch (error) {
      console.error('查看任务历史失败:', error);
      addLog('查看任务历史失败: ' + (error as Error).message);
    }
  };

  useEffect(() => {
    loadTasks();
  }, []);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold">任务流程记录演示</h1>
      
      {/* 操作面板 */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">操作控制</h2>
        <div className="flex gap-4 flex-wrap">
          <Button 
            onClick={createNewTask} 
            disabled={loading || !!currentTask}
          >
            {loading ? <LoadingSpinner size="sm" /> : '创建新任务'}
          </Button>
          
          <Button 
            onClick={runAnalysis} 
            disabled={loading || !currentTask}
            variant="secondary"
          >
            执行分析
          </Button>
          
          <Button 
            onClick={simulateChat} 
            disabled={loading || !currentTask}
            variant="secondary"
          >
            模拟对话
          </Button>
          
          <Button 
            onClick={completeTask} 
            disabled={loading || !currentTask}
            variant="secondary"
          >
            完成任务
          </Button>
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 当前任务信息 */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">当前任务</h2>
          {currentTask ? (
            <div className="space-y-2">
              <p><strong>ID:</strong> {currentTask.task_id}</p>
              <p><strong>股票代码:</strong> {currentTask.ticker}</p>
              <p><strong>标题:</strong> {currentTask.title}</p>
              <p><strong>状态:</strong> 
                <span className={`ml-2 px-2 py-1 rounded text-sm ${
                  currentTask.status === 'completed' ? 'bg-green-100 text-green-800' :
                  currentTask.status === 'running' ? 'bg-blue-100 text-blue-800' :
                  currentTask.status === 'failed' ? 'bg-red-100 text-red-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {currentTask.status}
                </span>
              </p>
              <p><strong>创建时间:</strong> {new Date(currentTask.created_at).toLocaleString()}</p>
            </div>
          ) : (
            <p className="text-gray-500">没有活跃的任务</p>
          )}
        </Card>

        {/* 任务概览 */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">任务概览</h2>
          {taskOverview ? (
            <div className="space-y-2">
              <p><strong>对话数量:</strong> {taskOverview.conversation_count}</p>
              <p><strong>消息数量:</strong> {taskOverview.message_count}</p>
              <p><strong>工具调用数量:</strong> {taskOverview.tool_call_count}</p>
              <p><strong>分析结果数量:</strong> {taskOverview.result_count}</p>
              <p><strong>持续时间:</strong> {Math.round(taskOverview.duration_seconds / 60)} 分钟</p>
              {taskOverview.last_activity_at && (
                <p><strong>最后活动:</strong> {new Date(taskOverview.last_activity_at).toLocaleString()}</p>
              )}
            </div>
          ) : (
            <p className="text-gray-500">没有概览数据</p>
          )}
        </Card>
      </div>

      {/* 任务历史 */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">任务历史</h2>
        {tasks.length > 0 ? (
          <div className="space-y-2">
            {tasks.map((task) => (
              <div key={task.task_id} className="flex items-center justify-between p-3 border rounded">
                <div>
                  <span className="font-medium">{task.ticker}</span>
                  <span className="mx-2">-</span>
                  <span>{task.title}</span>
                  <span className={`ml-2 px-2 py-1 rounded text-xs ${
                    task.status === 'completed' ? 'bg-green-100 text-green-800' :
                    task.status === 'running' ? 'bg-blue-100 text-blue-800' :
                    task.status === 'failed' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {task.status}
                  </span>
                </div>
                <Button 
                  size="sm" 
                  variant="secondary"
                  onClick={() => viewTaskHistory(task.task_id)}
                >
                  查看详情
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500">暂无任务历史</p>
        )}
      </Card>

      {/* 操作日志 */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">操作日志</h2>
        <div className="bg-gray-50 p-4 rounded max-h-96 overflow-y-auto">
          {logs.length > 0 ? (
            <pre className="text-sm whitespace-pre-wrap">
              {logs.join('\n')}
            </pre>
          ) : (
            <p className="text-gray-500">暂无日志</p>
          )}
        </div>
        {logs.length > 0 && (
          <Button 
            className="mt-2" 
            size="sm" 
            variant="secondary"
            onClick={() => setLogs([])}
          >
            清空日志
          </Button>
        )}
      </Card>
    </div>
  );
}
