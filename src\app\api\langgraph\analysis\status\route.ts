import { NextRequest, NextResponse } from 'next/server';
import { analysisStore } from '../start/route';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const analysisId = searchParams.get('analysisId');

    if (!analysisId) {
      return NextResponse.json(
        { error: '分析ID不能为空' },
        { status: 400 }
      );
    }

    const analysis = analysisStore.get(analysisId);
    
    if (!analysis) {
      return NextResponse.json(
        { error: '分析不存在' },
        { status: 404 }
      );
    }

    // 返回分析状态
    return NextResponse.json({
      analysisId: analysis.analysisId,
      status: analysis.status,
      currentStage: analysis.currentStage,
      progress: analysis.progress,
      isComplete: analysis.isComplete,
      startTime: analysis.startTime,
      endTime: analysis.endTime,
      lastUpdate: analysis.lastUpdate,
      error: analysis.error,
    });

  } catch (error) {
    console.error('获取分析状态失败:', error);
    return NextResponse.json(
      { error: '获取分析状态失败' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const analysisId = searchParams.get('analysisId');

    if (!analysisId) {
      return NextResponse.json(
        { error: '分析ID不能为空' },
        { status: 400 }
      );
    }

    const analysis = analysisStore.get(analysisId);
    
    if (!analysis) {
      return NextResponse.json(
        { error: '分析不存在' },
        { status: 404 }
      );
    }

    // 停止分析
    analysis.status = 'stopped';
    analysis.endTime = new Date().toISOString();
    analysisStore.set(analysisId, analysis);

    return NextResponse.json({
      message: '分析已停止',
      analysisId,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('停止分析失败:', error);
    return NextResponse.json(
      { error: '停止分析失败' },
      { status: 500 }
    );
  }
}
