// 数据库API路由 - 单个任务操作
import { NextRequest, NextResponse } from 'next/server';

// 使用外部定义的内存存储（实际项目中应该使用数据库）
let tasks: any[] = [];

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const taskId = params.id;
    const task = tasks.find(t => t.task_id === taskId);
    
    if (!task) {
      return NextResponse.json(
        { message: '任务不存在' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(task);
  } catch (error) {
    console.error('获取任务失败:', error);
    return NextResponse.json(
      { message: '获取任务失败' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const taskId = params.id;
    const body = await request.json();
    
    const taskIndex = tasks.findIndex(t => t.task_id === taskId);
    
    if (taskIndex === -1) {
      return NextResponse.json(
        { message: '任务不存在' },
        { status: 404 }
      );
    }
    
    // 更新任务
    tasks[taskIndex] = {
      ...tasks[taskIndex],
      ...body,
      updated_at: new Date(),
    };
    
    // 如果状态变为运行中，设置开始时间
    if (body.status === 'running' && !tasks[taskIndex].started_at) {
      tasks[taskIndex].started_at = new Date();
    }
    
    // 如果状态变为完成或失败，设置完成时间
    if (['completed', 'failed'].includes(body.status) && !tasks[taskIndex].completed_at) {
      tasks[taskIndex].completed_at = new Date();
    }
    
    return NextResponse.json({
      success: true,
      message: '任务更新成功',
    });
  } catch (error) {
    console.error('更新任务失败:', error);
    return NextResponse.json(
      { success: false, message: '更新任务失败' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const taskId = params.id;
    const taskIndex = tasks.findIndex(t => t.task_id === taskId);
    
    if (taskIndex === -1) {
      return NextResponse.json(
        { message: '任务不存在' },
        { status: 404 }
      );
    }
    
    tasks.splice(taskIndex, 1);
    
    return NextResponse.json({
      success: true,
      message: '任务删除成功',
    });
  } catch (error) {
    console.error('删除任务失败:', error);
    return NextResponse.json(
      { success: false, message: '删除任务失败' },
      { status: 500 }
    );
  }
}
