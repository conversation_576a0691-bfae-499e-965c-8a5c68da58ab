// 基础类型定义
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// 分析配置
export interface AnalysisConfig {
  ticker: string;
  analysisDate: string;
  selectedAnalysts: string[];
  llmProvider: string;
  deepThinkLlm: string;
  quickThinkLlm: string;
  maxDebateRounds: number;
  maxRiskDiscussRounds: number;
  onlineTools: boolean;
  researchDepth: 'quick' | 'standard' | 'deep';
}

// 分析状态
export interface AnalysisState {
  currentStage: string;
  progress: number;
  isComplete: boolean;
  error?: string;
  startTime?: string;
  endTime?: string;
}

// 代理状态
export interface AgentStatus {
  id: string;
  name: string;
  status: 'idle' | 'running' | 'completed' | 'error';
  progress: number;
  lastUpdate: string;
  message?: string;
  startTime?: string;
  endTime?: string;
}

// 分析报告
export interface AnalysisReport {
  type: string;
  title: string;
  content: string;
  timestamp: string;
  agent: string;
  metadata?: Record<string, any>;
}

// 交易决策
export interface TradingDecision {
  action: 'buy' | 'sell' | 'hold';
  confidence: number;
  reasoning: string;
  riskLevel: 'low' | 'medium' | 'high';
  targetPrice?: number;
  stopLoss?: number;
  positionSize?: number;
  timeHorizon?: string;
  timestamp: string;
  metadata?: {
    expectedReturn?: number;
    maxDrawdown?: number;
    sharpeRatio?: number;
    volatility?: number;
  };
}

// 股票数据
export interface StockData {
  ticker: string;
  currentPrice: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap?: number;
  priceHistory: PricePoint[];
  lastUpdate: string;
}

export interface PricePoint {
  time: string;
  price: number;
  volume?: number;
  high?: number;
  low?: number;
  open?: number;
  close?: number;
}

// 技术指标数据
export interface TechnicalData {
  ticker: string;
  indicators: Record<string, number>;
  chartData?: TechnicalChartPoint[];
  lastUpdate: string;
}

export interface TechnicalChartPoint {
  time: string;
  rsi?: number;
  macd?: number;
  signal?: number;
  histogram?: number;
  sma20?: number;
  sma50?: number;
  sma200?: number;
  bollinger_upper?: number;
  bollinger_lower?: number;
  bollinger_middle?: number;
}

// 新闻数据
export interface NewsData {
  ticker: string;
  articles: NewsArticle[];
  sentiment: {
    overall: 'positive' | 'negative' | 'neutral';
    score: number;
    distribution: {
      positive: number;
      negative: number;
      neutral: number;
    };
  };
  lastUpdate: string;
}

export interface NewsArticle {
  id: string;
  title: string;
  description: string;
  content?: string;
  source: string;
  author?: string;
  publishedAt: string;
  url?: string;
  sentiment?: 'positive' | 'negative' | 'neutral';
  sentimentScore?: number;
  relevance?: number;
}

// 基本面数据
export interface FundamentalsData {
  ticker: string;
  pe?: number;
  pb?: number;
  roe?: number;
  roa?: number;
  debtToEquity?: number;
  currentRatio?: number;
  quickRatio?: number;
  financials?: {
    revenue: number;
    netIncome: number;
    totalAssets: number;
    totalDebt: number;
    shareholders_equity: number;
    operatingCashFlow: number;
    freeCashFlow: number;
  };
  growth?: {
    revenueGrowth: number;
    earningsGrowth: number;
    dividendGrowth: number;
  };
  lastUpdate: string;
}

// WebSocket 消息类型
export interface WebSocketMessage {
  type: 'status_update' | 'agent_update' | 'new_report' | 'final_decision' | 'error' | 'ping' | 'pong';
  payload: any;
  timestamp: string;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 错误类型
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
}

// 用户偏好设置
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: 'zh-CN' | 'en-US';
  currency: 'USD' | 'CNY';
  timezone: string;
  notifications: {
    email: boolean;
    push: boolean;
    desktop: boolean;
  };
  dashboard: {
    defaultView: string;
    refreshInterval: number;
    showAdvancedMetrics: boolean;
  };
}

// 系统状态
export interface SystemStatus {
  status: 'healthy' | 'degraded' | 'down';
  version: string;
  uptime: number;
  services: {
    api: 'healthy' | 'degraded' | 'down';
    database: 'healthy' | 'degraded' | 'down';
    websocket: 'healthy' | 'degraded' | 'down';
    external_apis: 'healthy' | 'degraded' | 'down';
  };
  lastCheck: string;
}

// 分析历史
export interface AnalysisHistory extends BaseEntity {
  ticker: string;
  analysisDate: string;
  config: AnalysisConfig;
  status: 'pending' | 'running' | 'completed' | 'failed';
  result?: TradingDecision;
  duration?: number;
  reports: AnalysisReport[];
}

// 性能指标
export interface PerformanceMetrics {
  analysisId: string;
  ticker: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  stages: {
    [stageName: string]: {
      startTime: string;
      endTime?: string;
      duration?: number;
      status: 'pending' | 'running' | 'completed' | 'failed';
    };
  };
  agents: {
    [agentId: string]: {
      startTime: string;
      endTime?: string;
      duration?: number;
      status: 'pending' | 'running' | 'completed' | 'failed';
      tokensUsed?: number;
      apiCalls?: number;
    };
  };
  totalTokensUsed?: number;
  totalApiCalls?: number;
  cost?: number;
}


