import { NextRequest, NextResponse } from 'next/server';

// WebSocket 连接管理
const connections = new Map<string, WebSocket>();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const threadId = searchParams.get('threadId');

    if (!threadId) {
      return NextResponse.json(
        { error: '线程ID不能为空' },
        { status: 400 }
      );
    }

    // 注意：Next.js API Routes 不直接支持 WebSocket
    // 这里返回连接信息，但实际上我们使用轮询作为降级方案
    return NextResponse.json({
      message: 'WebSocket 功能暂不可用，使用轮询模式',
      threadId,
      wsUrl: null, // 设置为 null 表示 WebSocket 不可用
      fallbackMode: 'polling',
      pollingInterval: 2000, // 2秒轮询间隔
    });

  } catch (error) {
    console.error('WebSocket 信息获取失败:', error);
    return NextResponse.json(
      { error: 'WebSocket 信息获取失败' },
      { status: 500 }
    );
  }
}

// 发送消息到特定线程的所有连接
export function broadcastToThread(threadId: string, message: any) {
  const connection = connections.get(threadId);
  if (connection && connection.readyState === WebSocket.OPEN) {
    try {
      connection.send(JSON.stringify(message));
    } catch (error) {
      console.error('发送 WebSocket 消息失败:', error);
      connections.delete(threadId);
    }
  }
}

// 广播到所有连接
export function broadcastToAll(message: any) {
  for (const [threadId, connection] of connections.entries()) {
    if (connection.readyState === WebSocket.OPEN) {
      try {
        connection.send(JSON.stringify(message));
      } catch (error) {
        console.error('发送 WebSocket 消息失败:', error);
        connections.delete(threadId);
      }
    } else {
      connections.delete(threadId);
    }
  }
}
