import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { query } from '@/lib/db';

// 分析状态管理
const analysisStore = new Map<string, any>();

export async function POST(request: NextRequest) {
  try {
    const config = await request.json();
    const { ticker, researchDepth, analysisPeriod } = config;

    if (!ticker) {
      return NextResponse.json({ error: '股票代码不能为空' }, { status: 400 });
    }

    // 1. 将任务写入数据库
    const title = `分析 ${ticker}`;
    const description = `对 ${ticker} 进行的详细分析`;
    const configJson = JSON.stringify(config);
    const createdBy = 'frontend_user';

    const taskId = uuidv4();
    
    await query(
      'CALL CreateAnalysisTask(?, ?, ?, ?, ?, ?, ?, ?)',
      [taskId, ticker, title, description, configJson, createdBy, researchDepth, analysisPeriod]
    );

    // 生成分析ID (这里我们用 task_id 作为 analysisId 以保持一致)
    const analysisId = taskId;

    // 创建分析状态
    const analysisState = {
      analysisId,
      config,
      status: 'starting',
      currentStage: 'initialization',
      progress: 0,
      isComplete: false,
      startTime: new Date().toISOString(),
      agents: [
        {
          id: 'fundamental_analyst',
          name: '基本面分析师',
          status: 'initializing',
          progress: 0,
          lastUpdate: new Date().toISOString(),
        },
        {
          id: 'technical_analyst',
          name: '技术分析师',
          status: 'initializing',
          progress: 0,
          lastUpdate: new Date().toISOString(),
        },
        {
          id: 'sentiment_analyst',
          name: '情绪分析师',
          status: 'initializing',
          progress: 0,
          lastUpdate: new Date().toISOString(),
        },
        {
          id: 'risk_manager',
          name: '风险管理师',
          status: 'initializing',
          progress: 0,
          lastUpdate: new Date().toISOString(),
        },
        {
          id: 'trader',
          name: '交易员',
          status: 'waiting',
          progress: 0,
          lastUpdate: new Date().toISOString(),
        },
      ],
      reports: [],
      tradingDecision: null,
      error: null,
    };

    // 存储分析状态
    analysisStore.set(analysisId, analysisState);

    // 启动分析流程（异步）
    startAnalysisProcess(analysisId, config);

    return NextResponse.json({
      analysisId, // analysisId 就是 taskId
      status: 'started',
      message: '分析已启动',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('启动分析失败:', error);
    return NextResponse.json(
      { error: '启动分析失败，请稍后重试' },
      { status: 500 }
    );
  }
}

// 异步分析流程
async function startAnalysisProcess(analysisId: string, config: any) {
  const taskId = analysisId; // analysisId is the taskId
  try {
    const analysis = analysisStore.get(analysisId);
    if (!analysis) return;

    // 更新数据库和内存中的状态为 "running"
    await query('UPDATE tasks SET status = ?, started_at = NOW() WHERE task_id = ?', ['running', taskId]);
    analysis.status = 'running';
    analysis.currentStage = 'data_collection';
    analysis.progress = 10;
    analysisStore.set(analysisId, analysis);

    // 模拟分析步骤
    const stages = [
      { stage: 'data_collection', name: '数据收集', progress: 20, duration: 2000 },
      { stage: 'fundamental_analysis', name: '基本面分析', progress: 40, duration: 3000 },
      { stage: 'technical_analysis', name: '技术分析', progress: 60, duration: 2500 },
      { stage: 'sentiment_analysis', name: '情绪分析', progress: 80, duration: 2000 },
      { stage: 'risk_assessment', name: '风险评估', progress: 90, duration: 1500 },
      { stage: 'decision_making', name: '决策制定', progress: 100, duration: 1000 },
    ];

    for (const stageInfo of stages) {
      await new Promise(resolve => setTimeout(resolve, stageInfo.duration));
      
      const currentAnalysis = analysisStore.get(analysisId);
      if (!currentAnalysis) break;

      // 更新分析状态
      currentAnalysis.currentStage = stageInfo.stage;
      currentAnalysis.progress = stageInfo.progress;
      currentAnalysis.lastUpdate = new Date().toISOString();

      // 更新相关代理状态
      updateAgentStatus(currentAnalysis, stageInfo.stage);

      // 生成阶段报告
      if (stageInfo.progress > 20) {
        generateStageReport(currentAnalysis, stageInfo);
      }

      analysisStore.set(analysisId, currentAnalysis);
    }

    // 完成分析
    const finalAnalysis = analysisStore.get(analysisId);
    if (finalAnalysis) {
      await query('UPDATE tasks SET status = ?, completed_at = NOW() WHERE task_id = ?', ['completed', taskId]);
      finalAnalysis.status = 'completed';
      finalAnalysis.isComplete = true;
      finalAnalysis.endTime = new Date().toISOString();
      
      // 生成最终交易决策
      finalAnalysis.tradingDecision = generateTradingDecision(config);
      
      analysisStore.set(analysisId, finalAnalysis);
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    await query('UPDATE tasks SET status = ?, error_message = ? WHERE task_id = ?', ['failed', errorMessage, taskId]);
    
    console.error('分析流程失败:', error);
    const analysis = analysisStore.get(analysisId);
    if (analysis) {
      analysis.status = 'failed';
      analysis.error = errorMessage;
      analysisStore.set(analysisId, analysis);
    }
  }
}

// 更新代理状态
function updateAgentStatus(analysis: any, stage: string) {
  const agentMap: { [key: string]: string } = {
    'data_collection': 'fundamental_analyst',
    'fundamental_analysis': 'fundamental_analyst',
    'technical_analysis': 'technical_analyst',
    'sentiment_analysis': 'sentiment_analyst',
    'risk_assessment': 'risk_manager',
    'decision_making': 'trader',
  };

  const agentId = agentMap[stage];
  if (agentId) {
    const agent = analysis.agents.find((a: any) => a.id === agentId);
    if (agent) {
      agent.status = 'active';
      agent.progress = Math.min(100, agent.progress + 25);
      agent.lastUpdate = new Date().toISOString();
    }
  }
}

// 生成阶段报告
function generateStageReport(analysis: any, stageInfo: any) {
  const report = {
    id: `report_${Date.now()}`,
    stage: stageInfo.stage,
    title: `${stageInfo.name}报告`,
    content: `${stageInfo.name}已完成，发现以下关键信息...`,
    timestamp: new Date().toISOString(),
    confidence: Math.random() * 0.3 + 0.7, // 0.7-1.0
    recommendations: [
      `基于${stageInfo.name}的结果，建议...`,
      `需要关注的风险点包括...`,
    ],
  };

  analysis.reports.push(report);
}

// 生成交易决策
function generateTradingDecision(config: any) {
  const actions = ['buy', 'sell', 'hold'];
  const action = actions[Math.floor(Math.random() * actions.length)];
  
  return {
    action,
    confidence: Math.random() * 0.4 + 0.6, // 0.6-1.0
    reasoning: `基于综合分析，建议${action === 'buy' ? '买入' : action === 'sell' ? '卖出' : '持有'}`,
    targetPrice: config.ticker ? Math.random() * 200 + 100 : null,
    stopLoss: config.ticker ? Math.random() * 100 + 50 : null,
    riskLevel: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
    timestamp: new Date().toISOString(),
  };
}

// 导出分析存储以供其他API使用
export { analysisStore };
