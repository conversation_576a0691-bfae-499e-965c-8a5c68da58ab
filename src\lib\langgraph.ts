// 客户端兼容的 LangGraph 接口
// 实际的 LangGraph 功能已移至服务端

import { taskFlowDb } from './task-flow-database';
import { v4 as uuidv4 } from 'uuid';

// 基础类型定义
export interface BaseMessage {
  content: string;
  type?: string;
}

export interface HumanMessage extends BaseMessage {
  type: 'human';
}

export interface AIMessage extends BaseMessage {
  type: 'ai';
  tool_calls?: any[];
}

// 定义状态接口
export interface TradingAgentState {
  messages: BaseMessage[];
  ticker?: string;
  analysisConfig?: any;
  analysisResults?: any;
  tradingDecision?: any;
  riskAssessment?: any;
}

// 客户端兼容的交易代理类
export class TradingAgent {
  private taskId?: string;
  private conversationId?: string;

  /**
   * 初始化任务
   */
  async initializeTask(ticker: string, title: string, description?: string, config: any = {}) {
    try {
      const response = await taskFlowDb.createTask({
        ticker,
        title,
        description,
        config,
        created_by: 'user', // 可以从用户会话中获取
      });

      this.taskId = response.task_id;
      
      // 记录任务开始
      await taskFlowDb.updateTaskStatus(this.taskId, 'running');
      
      // 记录系统日志
      await taskFlowDb.logSystemEvent(
        'INFO',
        'TradingAgent',
        'initializeTask',
        `初始化分析任务: ${ticker}`,
        { ticker, title, config },
        this.taskId
      );

      return this.taskId;
    } catch (error) {
      console.error('初始化任务失败:', error);
      throw error;
    }
  }

  /**
   * 开始对话会话
   */
  async startConversation(threadId?: string, title?: string) {
    if (!this.taskId) {
      throw new Error('必须先初始化任务');
    }

    try {
      const response = await taskFlowDb.startConversation({
        task_id: this.taskId,
        thread_id: threadId || uuidv4(),
        title: title || '分析对话',
      });

      this.conversationId = response.conversation_id;
      return this.conversationId;
    } catch (error) {
      console.error('开始对话失败:', error);
      throw error;
    }
  }

  /**
   * 记录消息
   */
  private async recordMessage(
    type: 'human' | 'ai' | 'system' | 'tool',
    content: string,
    metadata?: any
  ) {
    if (!this.taskId || !this.conversationId) {
      throw new Error('必须先初始化任务和对话');
    }

    try {
      return await taskFlowDb.addMessage({
        conversation_id: this.conversationId,
        task_id: this.taskId,
        message_type: type,
        content,
        metadata,
      });
    } catch (error) {
      console.error('记录消息失败:', error);
      throw error;
    }
  }

  /**
   * 记录工具调用
   */
  private async recordToolCall(
    toolName: string,
    toolFunction: string,
    inputParameters?: any,
    messageId?: string
  ) {
    if (!this.taskId || !this.conversationId) {
      throw new Error('必须先初始化任务和对话');
    }

    try {
      return await taskFlowDb.recordToolCall({
        conversation_id: this.conversationId,
        task_id: this.taskId,
        tool_name: toolName,
        tool_function: toolFunction,
        input_parameters: inputParameters,
        message_id: messageId,
      });
    } catch (error) {
      console.error('记录工具调用失败:', error);
      throw error;
    }
  }

  async analyze(ticker: string, config: any = {}) {
    // 记录分析步骤开始
    const stepId = await taskFlowDb.recordAnalysisStep({
      step_id: uuidv4(),
      task_id: this.taskId!,
      conversation_id: this.conversationId,
      step_name: '股票分析',
      step_type: 'analysis',
      status: 'running',
      progress: 0,
      description: `分析股票 ${ticker}`,
      input_data: { ticker, config },
      sequence_order: 1,
    });

    try {
      // 记录用户消息
      await this.recordMessage('human', `请分析股票 ${ticker}`, { ticker, config });

      // 记录工具调用
      const toolCallResponse = await this.recordToolCall('api', 'analyze', { ticker, config });

      // 通过 API 调用服务端分析
      const response = await fetch('/api/langgraph/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ticker, config }),
      });

      if (!response.ok) {
        // 更新工具调用状态为失败
        await taskFlowDb.updateToolCall({
          tool_call_id: toolCallResponse.tool_call_id,
          status: 'failed',
          error_message: `分析请求失败: ${response.statusText}`,
        });

        // 更新分析步骤状态
        await taskFlowDb.updateAnalysisStep(stepId, {
          status: 'failed',
          progress: 0,
        });

        throw new Error(`分析请求失败: ${response.statusText}`);
      }

      const result = await response.json();

      // 更新工具调用状态为成功
      await taskFlowDb.updateToolCall({
        tool_call_id: toolCallResponse.tool_call_id,
        status: 'completed',
        output_result: result,
      });

      // 记录AI回复
      await this.recordMessage('ai', '分析完成', { result });

      // 保存分析结果
      await taskFlowDb.saveAnalysisResult({
        task_id: this.taskId!,
        conversation_id: this.conversationId,
        result_type: 'comprehensive',
        result_data: result,
        summary: `${ticker} 股票分析结果`,
        is_final: true,
      });

      // 更新分析步骤状态
      await taskFlowDb.updateAnalysisStep(stepId, {
        status: 'completed',
        progress: 100,
        output_data: result,
      });

      return result;
    } catch (error) {
      // 更新分析步骤状态为失败
      await taskFlowDb.updateAnalysisStep(stepId, {
        status: 'failed',
        progress: 0,
      });

      throw error;
    }
  }

  async chat(message: string, threadId: string) {
    try {
      // 记录用户消息
      const userMessageResponse = await this.recordMessage('human', message);

      // 记录工具调用
      const toolCallResponse = await this.recordToolCall(
        'api',
        'chat',
        { message, threadId },
        userMessageResponse.message_id
      );

      // 通过 API 调用服务端聊天
      const response = await fetch('/api/langgraph/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message, threadId }),
      });

      if (!response.ok) {
        // 更新工具调用状态为失败
        await taskFlowDb.updateToolCall({
          tool_call_id: toolCallResponse.tool_call_id,
          status: 'failed',
          error_message: `聊天请求失败: ${response.statusText}`,
        });

        throw new Error(`聊天请求失败: ${response.statusText}`);
      }

      const result = await response.json();

      // 更新工具调用状态为成功
      await taskFlowDb.updateToolCall({
        tool_call_id: toolCallResponse.tool_call_id,
        status: 'completed',
        output_result: result,
      });

      // 记录AI回复
      await this.recordMessage('ai', result.response || 'AI回复', { result });

      return result;
    } catch (error) {
      console.error('聊天失败:', error);
      throw error;
    }
  }

  /**
   * 完成任务
   */
  async completeTask() {
    if (!this.taskId) {
      throw new Error('没有活跃的任务');
    }

    try {
      await taskFlowDb.updateTaskStatus(this.taskId, 'completed');
      
      if (this.conversationId) {
        await taskFlowDb.endConversation(this.conversationId);
      }

      // 记录任务完成日志
      await taskFlowDb.logSystemEvent(
        'INFO',
        'TradingAgent',
        'completeTask',
        '任务分析完成',
        {},
        this.taskId
      );

      // 清理状态
      this.taskId = undefined;
      this.conversationId = undefined;
    } catch (error) {
      console.error('完成任务失败:', error);
      throw error;
    }
  }

  /**
   * 获取任务概览
   */
  async getTaskOverview(taskId?: string) {
    const id = taskId || this.taskId;
    if (!id) {
      throw new Error('没有指定任务ID');
    }

    return await taskFlowDb.getTaskOverview(id);
  }

  /**
   * 获取任务的完整记录
   */
  async getTaskHistory(taskId?: string) {
    const id = taskId || this.taskId;
    if (!id) {
      throw new Error('没有指定任务ID');
    }

    const [
      task,
      conversations,
      messages,
      toolCalls,
      analysisResults,
      analysisSteps,
    ] = await Promise.all([
      taskFlowDb.getTask(id),
      taskFlowDb.getTaskConversations(id),
      taskFlowDb.getTaskMessages(id),
      taskFlowDb.getTaskToolCalls(id),
      taskFlowDb.getTaskAnalysisResults(id),
      taskFlowDb.getTaskAnalysisSteps(id),
    ]);

    return {
      task,
      conversations,
      messages,
      toolCalls,
      analysisResults,
      analysisSteps,
    };
  }
}

// 创建 HumanMessage 辅助函数
export function createHumanMessage(content: string): HumanMessage {
  return {
    type: 'human',
    content,
  };
}

// 导出默认实例
export const tradingAgent = new TradingAgent();
