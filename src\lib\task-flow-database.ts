// 数据库访问层 - 任务全流程记录
import {
  Task,
  Conversation,
  Message,
  ToolCall,
  AnalysisResult,
  AnalysisStep,
  CreateTaskRequest,
  CreateTaskResponse,
  StartConversationRequest,
  StartConversationResponse,
  AddMessageRequest,
  AddMessageResponse,
  RecordToolCallRequest,
  RecordToolCallResponse,
  UpdateToolCallRequest,
  SaveAnalysisResultRequest,
  SaveAnalysisResultResponse,
  TaskQueryOptions,
  TaskOverview,
  TaskStatistics,
} from '@/types/database';

export class TaskFlowDatabase {
  private baseUrl = '/api/database';

  // ===== 任务相关操作 =====
  
  /**
   * 创建新的分析任务
   */
  async createTask(request: CreateTaskRequest): Promise<CreateTaskResponse> {
    const response = await fetch(`${this.baseUrl}/tasks`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error(`创建任务失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 获取任务详情
   */
  async getTask(taskId: string): Promise<Task | null> {
    const response = await fetch(`${this.baseUrl}/tasks/${taskId}`);
    
    if (response.status === 404) {
      return null;
    }
    
    if (!response.ok) {
      throw new Error(`获取任务失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 更新任务状态
   */
  async updateTaskStatus(
    taskId: string,
    status: Task['status'],
    errorMessage?: string
  ): Promise<void> {
    const response = await fetch(`${this.baseUrl}/tasks/${taskId}/status`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ status, error_message: errorMessage }),
    });
    
    if (!response.ok) {
      throw new Error(`更新任务状态失败: ${response.statusText}`);
    }
  }

  /**
   * 查询任务列表
   */
  async queryTasks(options: TaskQueryOptions = {}): Promise<Task[]> {
    const params = new URLSearchParams();
    
    if (options.status) params.append('status', options.status.join(','));
    if (options.ticker) params.append('ticker', options.ticker);
    if (options.created_by) params.append('created_by', options.created_by);
    if (options.date_from) params.append('date_from', options.date_from.toISOString());
    if (options.date_to) params.append('date_to', options.date_to.toISOString());
    if (options.limit) params.append('limit', options.limit.toString());
    if (options.offset) params.append('offset', options.offset.toString());

    const response = await fetch(`${this.baseUrl}/tasks?${params}`);
    
    if (!response.ok) {
      throw new Error(`查询任务失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 获取任务概览
   */
  async getTaskOverview(taskId: string): Promise<TaskOverview | null> {
    const response = await fetch(`${this.baseUrl}/tasks/${taskId}/overview`);
    
    if (response.status === 404) {
      return null;
    }
    
    if (!response.ok) {
      throw new Error(`获取任务概览失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  // ===== 对话相关操作 =====
  
  /**
   * 开始新的对话会话
   */
  async startConversation(request: StartConversationRequest): Promise<StartConversationResponse> {
    const response = await fetch(`${this.baseUrl}/conversations`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error(`开始对话失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 获取对话会话
   */
  async getConversation(conversationId: string): Promise<Conversation | null> {
    const response = await fetch(`${this.baseUrl}/conversations/${conversationId}`);
    
    if (response.status === 404) {
      return null;
    }
    
    if (!response.ok) {
      throw new Error(`获取对话失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 获取任务的所有对话
   */
  async getTaskConversations(taskId: string): Promise<Conversation[]> {
    const response = await fetch(`${this.baseUrl}/tasks/${taskId}/conversations`);
    
    if (!response.ok) {
      throw new Error(`获取任务对话失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 结束对话会话
   */
  async endConversation(conversationId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/conversations/${conversationId}/end`, {
      method: 'PATCH',
    });
    
    if (!response.ok) {
      throw new Error(`结束对话失败: ${response.statusText}`);
    }
  }

  // ===== 消息相关操作 =====
  
  /**
   * 添加新消息
   */
  async addMessage(request: AddMessageRequest): Promise<AddMessageResponse> {
    const response = await fetch(`${this.baseUrl}/messages`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error(`添加消息失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 获取对话的所有消息
   */
  async getConversationMessages(conversationId: string): Promise<Message[]> {
    const response = await fetch(`${this.baseUrl}/conversations/${conversationId}/messages`);
    
    if (!response.ok) {
      throw new Error(`获取对话消息失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 获取任务的所有消息
   */
  async getTaskMessages(taskId: string): Promise<Message[]> {
    const response = await fetch(`${this.baseUrl}/tasks/${taskId}/messages`);
    
    if (!response.ok) {
      throw new Error(`获取任务消息失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  // ===== 工具调用相关操作 =====
  
  /**
   * 记录工具调用
   */
  async recordToolCall(request: RecordToolCallRequest): Promise<RecordToolCallResponse> {
    const response = await fetch(`${this.baseUrl}/tool-calls`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error(`记录工具调用失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 更新工具调用结果
   */
  async updateToolCall(request: UpdateToolCallRequest): Promise<void> {
    const response = await fetch(`${this.baseUrl}/tool-calls/${request.tool_call_id}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error(`更新工具调用失败: ${response.statusText}`);
    }
  }

  /**
   * 获取任务的工具调用记录
   */
  async getTaskToolCalls(taskId: string): Promise<ToolCall[]> {
    const response = await fetch(`${this.baseUrl}/tasks/${taskId}/tool-calls`);
    
    if (!response.ok) {
      throw new Error(`获取工具调用记录失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  // ===== 分析结果相关操作 =====
  
  /**
   * 保存分析结果
   */
  async saveAnalysisResult(request: SaveAnalysisResultRequest): Promise<SaveAnalysisResultResponse> {
    const response = await fetch(`${this.baseUrl}/analysis-results`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error(`保存分析结果失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 获取任务的分析结果
   */
  async getTaskAnalysisResults(taskId: string): Promise<AnalysisResult[]> {
    const response = await fetch(`${this.baseUrl}/tasks/${taskId}/analysis-results`);
    
    if (!response.ok) {
      throw new Error(`获取分析结果失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 获取最终分析结果
   */
  async getFinalAnalysisResult(taskId: string): Promise<AnalysisResult | null> {
    const response = await fetch(`${this.baseUrl}/tasks/${taskId}/final-result`);
    
    if (response.status === 404) {
      return null;
    }
    
    if (!response.ok) {
      throw new Error(`获取最终结果失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  // ===== 分析步骤相关操作 =====
  
  /**
   * 记录分析步骤
   */
  async recordAnalysisStep(step: Omit<AnalysisStep, 'id' | 'created_at'>): Promise<string> {
    const response = await fetch(`${this.baseUrl}/analysis-steps`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(step),
    });
    
    if (!response.ok) {
      throw new Error(`记录分析步骤失败: ${response.statusText}`);
    }
    
    const result = await response.json();
    return result.step_id;
  }

  /**
   * 更新分析步骤
   */
  async updateAnalysisStep(
    stepId: string,
    updates: Partial<Pick<AnalysisStep, 'status' | 'progress' | 'output_data' | 'metrics' | 'duration_ms'>>
  ): Promise<void> {
    const response = await fetch(`${this.baseUrl}/analysis-steps/${stepId}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updates),
    });
    
    if (!response.ok) {
      throw new Error(`更新分析步骤失败: ${response.statusText}`);
    }
  }

  /**
   * 获取任务的分析步骤
   */
  async getTaskAnalysisSteps(taskId: string): Promise<AnalysisStep[]> {
    const response = await fetch(`${this.baseUrl}/tasks/${taskId}/analysis-steps`);
    
    if (!response.ok) {
      throw new Error(`获取分析步骤失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  // ===== 统计和监控 =====
  
  /**
   * 获取任务统计信息
   */
  async getTaskStatistics(dateFrom?: Date, dateTo?: Date): Promise<TaskStatistics> {
    const params = new URLSearchParams();
    if (dateFrom) params.append('date_from', dateFrom.toISOString());
    if (dateTo) params.append('date_to', dateTo.toISOString());

    const response = await fetch(`${this.baseUrl}/statistics/tasks?${params}`);
    
    if (!response.ok) {
      throw new Error(`获取统计信息失败: ${response.statusText}`);
    }
    
    return response.json();
  }

  /**
   * 获取活跃任务
   */
  async getActiveTasks(): Promise<Task[]> {
    return this.queryTasks({ status: ['running'] });
  }

  /**
   * 记录系统日志
   */
  async logSystemEvent(
    level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR' | 'FATAL',
    component: string,
    operation: string,
    message: string,
    details?: any,
    taskId?: string
  ): Promise<void> {
    const response = await fetch(`${this.baseUrl}/logs`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        log_level: level,
        component,
        operation,
        message,
        details,
        task_id: taskId,
      }),
    });
    
    if (!response.ok) {
      console.warn(`记录系统日志失败: ${response.statusText}`);
    }
  }
}

// 导出默认实例
export const taskFlowDb = new TaskFlowDatabase();
