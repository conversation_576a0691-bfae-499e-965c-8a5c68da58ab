'use client';

import { useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion, useScroll } from 'framer-motion';
import { ChartBarIcon, PlayIcon, EyeIcon, ArrowDownIcon } from '@heroicons/react/24/outline';

export default function HomePage() {
  const router = useRouter();
  const containerRef = useRef<HTMLDivElement>(null);
  const [currentScreen, setCurrentScreen] = useState(0);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  // 双向自动滚动逻辑
  useEffect(() => {
    let isScrolling = false;

    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const windowHeight = window.innerHeight;

      // 更新当前屏幕状态
      const screen = Math.round(scrollPosition / windowHeight);
      setCurrentScreen(screen);

      if (isScrolling) return;

      // 向下滚动：当滚动超过 25% 但小于 75% 时自动滚动到第二屏
      if (scrollPosition > windowHeight * 0.25 && scrollPosition < windowHeight * 0.75) {
        isScrolling = true;
        window.scrollTo({
          top: windowHeight,
          behavior: 'smooth'
        });
        setTimeout(() => { isScrolling = false; }, 1000);
      }
      // 向上滚动：当在第二屏但滚动位置小于 125% 时自动滚动回第一屏
      else if (scrollPosition > windowHeight * 0.75 && scrollPosition < windowHeight * 1.25) {
        isScrolling = true;
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
        setTimeout(() => { isScrolling = false; }, 1000);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // 键盘导航
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'ArrowDown' && currentScreen === 0) {
        event.preventDefault();
        scrollToScreen(1);
      } else if (event.key === 'ArrowUp' && currentScreen === 1) {
        event.preventDefault();
        scrollToScreen(0);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentScreen]);

  const handleQuickStart = () => {
    router.push('/create-task');
  };

  const handleViewExamples = () => {
    router.push('/test-analysis');
  };

  // 手动滚动到指定屏幕
  const scrollToScreen = (screenNumber: number) => {
    const windowHeight = window.innerHeight;
    window.scrollTo({
      top: screenNumber * windowHeight,
      behavior: 'smooth'
    });
  };

  return (
    <div ref={containerRef} className="relative">
      {/* 屏幕导航指示器 */}
      <div className="fixed right-6 top-1/2 transform -translate-y-1/2 z-40 space-y-3">
        <button
          onClick={() => scrollToScreen(0)}
          className={`w-3 h-3 rounded-full border-2 transition-all duration-300 ${
            currentScreen === 0
              ? 'border-blue-500 bg-blue-500'
              : 'border-slate-400 bg-white hover:border-blue-500 hover:bg-blue-500'
          }`}
          title="返回首页"
        />
        <button
          onClick={() => scrollToScreen(1)}
          className={`w-3 h-3 rounded-full border-2 transition-all duration-300 ${
            currentScreen === 1
              ? 'border-blue-500 bg-blue-500'
              : 'border-slate-400 bg-white hover:border-blue-500 hover:bg-blue-500'
          }`}
          title="功能特性"
        />
      </div>

      {/* 第一屏 - 首页 */}
      <section className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-900 relative overflow-hidden"
        id="home"
      >
        {/* 主体内容 */}
        <div className="flex flex-col justify-center items-center min-h-screen px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            {/* 口号 */}
            <h1 className="text-5xl md:text-7xl font-bold text-slate-900 dark:text-white mb-6 leading-tight">
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                TradingAgents
              </span>
              <br />
              多智能体交易框架
            </h1>

            <p className="text-xl md:text-2xl text-slate-600 dark:text-slate-300 mb-8 leading-relaxed">
              模拟真实交易公司运作模式的专业AI交易系统
            </p>

            <div className="flex flex-wrap justify-center gap-4 mb-12 text-sm md:text-base">
              <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm px-4 py-2 rounded-full border border-slate-200 dark:border-slate-600">
                <span className="text-blue-600 font-semibold">📊 基本面分析师</span>
              </div>
              <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm px-4 py-2 rounded-full border border-slate-200 dark:border-slate-600">
                <span className="text-purple-600 font-semibold">📈 技术分析师</span>
              </div>
              <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm px-4 py-2 rounded-full border border-slate-200 dark:border-slate-600">
                <span className="text-green-600 font-semibold">💭 情绪分析师</span>
              </div>
              <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm px-4 py-2 rounded-full border border-slate-200 dark:border-slate-600">
                <span className="text-orange-600 font-semibold">🛡️ 风险管理师</span>
              </div>
              <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm px-4 py-2 rounded-full border border-slate-200 dark:border-slate-600">
                <span className="text-red-600 font-semibold">💼 交易员</span>
              </div>
            </div>

            {/* 按钮组 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <button
                onClick={handleQuickStart}
                className="group bg-blue-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center space-x-2"
              >
                <PlayIcon className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
                <span>快速开始</span>
              </button>

              <button
                onClick={handleViewExamples}
                className="group bg-white dark:bg-slate-800 text-slate-900 dark:text-white px-8 py-4 rounded-xl text-lg font-semibold border-2 border-slate-200 dark:border-slate-600 hover:border-blue-600 dark:hover:border-blue-400 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center space-x-2"
              >
                <EyeIcon className="h-5 w-5 group-hover:scale-110 transition-transform" />
                <span>查看案例</span>
              </button>
            </motion.div>
          </motion.div>

          {/* 滚动提示 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 1 }}
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          >
            <div className="flex flex-col items-center space-y-2 text-slate-400 dark:text-slate-500">
              <span className="text-sm">向下滚动探索更多</span>
              <span className="text-xs opacity-75">或使用 ↓ 键</span>
              <motion.div
                animate={{ y: [0, 8, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="w-6 h-10 border-2 border-slate-300 dark:border-slate-600 rounded-full flex justify-center"
              >
                <motion.div
                  animate={{ y: [0, 12, 0] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="w-1 h-3 bg-slate-400 dark:bg-slate-500 rounded-full mt-2"
                />
              </motion.div>
            </div>
          </motion.div>
        </div>

        {/* 第二屏预览区域 */}
        <div className="absolute bottom-0 left-0 right-0 h-1/4 bg-gradient-to-t from-slate-100 to-transparent dark:from-slate-800 dark:to-transparent opacity-50" />
      </section>
      {/* 第二屏 - 功能展示 */}
      <section className="min-h-screen bg-white dark:bg-slate-900 relative flex items-center justify-center"
        id="features"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-6xl font-bold text-slate-900 dark:text-white mb-6">
              专业智能体团队
            </h2>
            <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              模拟真实交易公司的专业分工，每个智能体都有明确的职责和专业领域
            </p>
          </motion.div>

          {/* 智能体团队网格 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* 分析师团队 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 p-8 rounded-2xl border border-blue-200 dark:border-blue-800 hover:shadow-xl transition-all duration-300"
            >
              <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mb-6">
                <ChartBarIcon className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">
                分析师团队
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                基本面分析师、情绪分析师、新闻分析师、技术分析师协同工作
              </p>
              <div className="text-sm text-blue-600 dark:text-blue-400">
                • 评估公司财务与绩效指标<br/>
                • 分析社交媒体与公众情绪<br/>
                • 运用技术指标预测价格走势
              </div>
            </motion.div>

            {/* 研究团队 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-purple-50 to-pink-100 dark:from-purple-900/20 dark:to-pink-900/20 p-8 rounded-2xl border border-purple-200 dark:border-purple-800 hover:shadow-xl transition-all duration-300"
            >
              <div className="w-12 h-12 bg-purple-600 rounded-xl flex items-center justify-center mb-6">
                <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">
                研究团队
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                多头和空头研究员通过结构化辩论平衡潜在收益与风险
              </p>
              <div className="text-sm text-purple-600 dark:text-purple-400">
                • 批判性评估分析师见解<br/>
                • 结构化辩论权衡风险收益<br/>
                • 提供多角度投资观点
              </div>
            </motion.div>

            {/* 交易员智能体 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20 p-8 rounded-2xl border border-green-200 dark:border-green-800 hover:shadow-xl transition-all duration-300"
            >
              <div className="w-12 h-12 bg-green-600 rounded-xl flex items-center justify-center mb-6">
                <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">
                交易员代理
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                基于分析师和研究员的综合报告做出明智的交易决策
              </p>
              <div className="text-sm text-green-600 dark:text-green-400">
                • 整合多方分析报告<br/>
                • 确定最佳交易时机<br/>
                • 制定交易规模策略
              </div>
            </motion.div>

            {/* 风险管理团队 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-orange-50 to-red-100 dark:from-orange-900/20 dark:to-red-900/20 p-8 rounded-2xl border border-orange-200 dark:border-orange-800 hover:shadow-xl transition-all duration-300"
            >
              <div className="w-12 h-12 bg-orange-600 rounded-xl flex items-center justify-center mb-6">
                <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">
                风险管理团队
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                持续评估投资组合风险，调整交易策略并提供风险评估
              </p>
              <div className="text-sm text-orange-600 dark:text-orange-400">
                • 分析市场波动性和流动性<br/>
                • 评估并调整交易策略<br/>
                • 向投资组合经理提交报告
              </div>
            </motion.div>

            {/* 投资组合经理 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-cyan-50 to-blue-100 dark:from-cyan-900/20 dark:to-blue-900/20 p-8 rounded-2xl border border-cyan-200 dark:border-cyan-800 hover:shadow-xl transition-all duration-300"
            >
              <div className="w-12 h-12 bg-cyan-600 rounded-xl flex items-center justify-center mb-6">
                <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">
                投资组合经理
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                审批交易提案，若获批准则发送至模拟交易所执行
              </p>
              <div className="text-sm text-cyan-600 dark:text-cyan-400">
                • 最终交易决策审批<br/>
                • 投资组合优化管理<br/>
                • 模拟交易所订单执行
              </div>
            </motion.div>

            {/* LangGraph 架构 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-indigo-50 to-purple-100 dark:from-indigo-900/20 dark:to-purple-900/20 p-8 rounded-2xl border border-indigo-200 dark:border-indigo-800 hover:shadow-xl transition-all duration-300"
            >
              <div className="w-12 h-12 bg-indigo-600 rounded-xl flex items-center justify-center mb-6">
                <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">
                LangGraph 架构
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                采用 LangGraph 构建，确保灵活性与模块化设计
              </p>
              <div className="text-sm text-indigo-600 dark:text-indigo-400">
                • 支持 o1-preview 和 gpt-4o 模型<br/>
                • 灵活的配置和扩展能力<br/>
                • 模块化的智能体架构
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* 案例展示区域 */}
      <section id="examples" className="py-20 bg-slate-50 dark:bg-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-8">
            案例展示
          </h2>
          <p className="text-lg text-slate-600 dark:text-slate-300 mb-8">
            查看我们的AI交易分析系统在实际市场中的表现
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white dark:bg-slate-700 p-6 rounded-lg shadow-lg">
              <h3 className="text-xl font-semibold mb-4">AAPL 分析案例</h3>
              <p className="text-slate-600 dark:text-slate-300">苹果公司股票的综合分析报告</p>
            </div>
            <div className="bg-white dark:bg-slate-700 p-6 rounded-lg shadow-lg">
              <h3 className="text-xl font-semibold mb-4">TSLA 分析案例</h3>
              <p className="text-slate-600 dark:text-slate-300">特斯拉股票的技术与基本面分析</p>
            </div>
            <div className="bg-white dark:bg-slate-700 p-6 rounded-lg shadow-lg">
              <h3 className="text-xl font-semibold mb-4">NVDA 分析案例</h3>
              <p className="text-slate-600 dark:text-slate-300">英伟达股票的AI驱动分析</p>
            </div>
          </div>
        </div>
      </section>

      {/* 文档区域 */}
      <section id="docs" className="py-20 bg-white dark:bg-slate-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-8">
            文档与资源
          </h2>
          <p className="text-lg text-slate-600 dark:text-slate-300 mb-8">
            了解如何使用我们的多智能体交易分析框架
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <a href="https://github.com/TauricResearch/TradingAgents" target="_blank" className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg hover:shadow-lg transition-shadow">
              <h3 className="text-lg font-semibold mb-2">快速开始</h3>
              <p className="text-sm text-slate-600 dark:text-slate-300">安装和配置指南</p>
            </a>
            <a href="https://arxiv.org/abs/2412.20138" target="_blank" className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg hover:shadow-lg transition-shadow">
              <h3 className="text-lg font-semibold mb-2">API 文档</h3>
              <p className="text-sm text-slate-600 dark:text-slate-300">完整的API参考</p>
            </a>
            <a href="#" className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg hover:shadow-lg transition-shadow">
              <h3 className="text-lg font-semibold mb-2">教程</h3>
              <p className="text-sm text-slate-600 dark:text-slate-300">逐步学习指南</p>
            </a>
            <a href="#" className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg hover:shadow-lg transition-shadow">
              <h3 className="text-lg font-semibold mb-2">社区</h3>
              <p className="text-sm text-slate-600 dark:text-slate-300">加入我们的社区</p>
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
